//
//  FriendsLogViewModel.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/12.
//

import Foundation
import Combine

// MARK: - 日志分组模型

/// 按日期分组的日志数据（朋友圈专用）
struct FriendsDayGroup: Identifiable {
    let id = UUID()
    let date: Date
    let items: [UserLog]

    /// 格式化的日期字符串
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.dateFormat = "M月d日"
        return formatter.string(from: date)
    }
}

// MARK: - 朋友圈日志视图模型

/// 朋友圈日志视图模型，负责获取所有好友的公开日志并按日期分组
@MainActor
class FriendsLogViewModel: ObservableObject {

    // MARK: - Published Properties

    /// 按日期分组的好友日志
    @Published var dayGroups: [FriendsDayGroup] = []

    /// 所有类型的日志（用于筛选）
    @Published var allLogs: [UserLog] = []
    @Published var locationLogs: [UserLog] = []
    @Published var tripLogs: [UserLog] = []
    @Published var recognitionLogs: [UserLog] = []

    /// 当前选择的记录类型筛选
    @Published var selectedRecordType: RecordType? = nil

    /// 日期筛选
    @Published var startDate: Date? = nil
    @Published var endDate: Date? = nil

    /// 是否正在加载
    @Published var isLoading: Bool = false

    /// 错误信息
    @Published var errorMessage: String?

    /// 成功信息
    @Published var successMessage: String?

    // MARK: - Private Properties

    private let userLogManager = UserLogManager()
    private let friendViewModel: FriendViewModel
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization

    init(friendViewModel: FriendViewModel) {
        self.friendViewModel = friendViewModel
    }

    // MARK: - Public Methods

    /// 获取所有好友的公开日志
    func fetchFriendsLogs(currentUserId: String) async {
        isLoading = true
        errorMessage = nil

        // 首先确保已获取好友列表
        if friendViewModel.friendListData == nil {
            await friendViewModel.fetchFriendList(for: currentUserId)
        }

        // 获取所有好友的ID
        let friendIds = friendViewModel.friends.map { $0.friend.userId }

        guard !friendIds.isEmpty else {
            print("📝 没有好友，无法获取朋友圈日志")
            isLoading = false
            return
        }

        print("📝 开始获取 \(friendIds.count) 个好友的公开日志")

        // 并发获取所有好友的公开日志
        let allFriendsLogs = await withTaskGroup(of: [UserLog].self) { group in
            // 为每个好友创建一个任务
            for friendId in friendIds {
                group.addTask {
                    await self.fetchUserPublicLogs(userId: friendId)
                }
            }

            // 收集所有结果
            var logs: [UserLog] = []
            for await friendLogs in group {
                logs.append(contentsOf: friendLogs)
            }
            return logs
        }

        // 按创建时间降序排序
        let sortedLogs = allFriendsLogs.sorted { $0.createdAt > $1.createdAt }

        // 更新UI（确保在主线程上执行）
        self.allLogs = sortedLogs
        self.updateFilteredLogs()
        self.groupLogsByDate()

        isLoading = false
        print("✅ 朋友圈日志获取完成，共 \(allLogs.count) 条记录")
    }

    /// 获取指定用户的公开日志
    private func fetchUserPublicLogs(userId: String, recordType: RecordType? = nil) async -> [UserLog] {
        do {
            let queryParams = UserLogQueryParams(
                userId: userId,
                recordType: recordType,
                isPublic: true, // 只获取公开日志
                page: 1,
                limit: 100 // 获取更多数据
            )

            let logListData = try await userLogManager.fetchUserLogs(queryParams)
            print("✅ 获取用户 \(userId) 的公开日志成功，共 \(logListData.logs.count) 条")
            return logListData.logs

        } catch {
            print("❌ 获取用户 \(userId) 的公开日志失败: \(error)")
            return []
        }
    }

    /// 应用筛选条件
    func applyFilters(currentUserId: String) async {
        if selectedRecordType != nil || startDate != nil || endDate != nil {
            // 如果有筛选条件，重新获取数据
            await fetchFriendsLogs(currentUserId: currentUserId)
        } else {
            // 如果没有筛选条件，使用所有日志
            updateFilteredLogs()
            groupLogsByDate()
        }
    }

    /// 更新按类型筛选的日志数组
    private func updateFilteredLogs() {
        locationLogs = allLogs.filter { $0.recordType == .location }
        tripLogs = allLogs.filter { $0.recordType == .trip }
        recognitionLogs = allLogs.filter { $0.recordType == .recognition }
    }

    /// 按日期分组日志
    private func groupLogsByDate() {
        let logsToGroup: [UserLog]

        // 根据选择的类型确定要分组的日志
        switch selectedRecordType {
        case .location:
            logsToGroup = locationLogs
        case .trip:
            logsToGroup = tripLogs
        case .recognition:
            logsToGroup = recognitionLogs
        case nil:
            logsToGroup = allLogs
        }

        // 按日期分组
        let calendar = Calendar.current
        let grouped = Dictionary(grouping: logsToGroup) { log in
            calendar.startOfDay(for: log.createdAt)
        }

        // 转换为FriendsDayGroup数组并按日期降序排序
        dayGroups = grouped.map { date, logs in
            FriendsDayGroup(date: date, items: logs.sorted { $0.createdAt > $1.createdAt })
        }.sorted { $0.date > $1.date }

        print("📅 日志按日期分组完成，共 \(dayGroups.count) 个日期组")
    }

    /// 清除筛选条件
    func clearFilters() {
        selectedRecordType = nil
        startDate = nil
        endDate = nil
        updateFilteredLogs()
        groupLogsByDate()
    }

    /// 刷新朋友圈日志
    func refreshLogs(currentUserId: String) async {
        await fetchFriendsLogs(currentUserId: currentUserId)
    }

    /// 清除错误信息
    func clearErrorMessage() {
        errorMessage = nil
    }

    /// 清除成功信息
    func clearSuccessMessage() {
        successMessage = nil
    }
}
