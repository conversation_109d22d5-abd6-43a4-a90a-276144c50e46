//
//  LogViewModel.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/3.
//

import Foundation
import SwiftUI
import Combine

// MARK: - 日志视图模型

/// 日志视图模型，负责调用服务获取指定用户ID的日志记录，以数组形式提供数据供UI组件渲染，同时管理日志的评论和点赞状态
@MainActor
class LogViewModel: ObservableObject {

    // MARK: - Published Properties

    /// 用户日志列表
    @Published var userLogs: [UserLog] = []

    /// 分页信息
    @Published var paginationInfo: PaginationInfo?

    /// 是否正在加载
    @Published var isLoading: Bool = false

    /// 是否正在加载更多
    @Published var isLoadingMore: Bool = false

    /// 错误信息
    @Published var errorMessage: String?

    /// 成功信息
    @Published var successMessage: String?

    /// 当前查询参数
    @Published var currentQueryParams: UserLogQueryParams?

    /// 筛选条件
    @Published var selectedRecordType: RecordType?
    @Published var showPublicOnly: Bool = true

    // MARK: - Private Properties

    private let userLogManager: UserLogManagerProtocol
    private let logDetailManager: LogDetailManagerProtocol
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization

    init(userLogManager: UserLogManagerProtocol? = nil,
         logDetailManager: LogDetailManagerProtocol? = nil) {
        self.userLogManager = userLogManager ?? UserLogManager()
        self.logDetailManager = logDetailManager ?? LogDetailManager()
    }

    // MARK: - 日志查询方法

    /// 获取用户日志列表（首次加载）
    func fetchUserLogs(userId: String,
                      recordType: RecordType? = nil,
                      isPublic: Bool? = nil,
                      startDate: Date? = nil,
                      endDate: Date? = nil,
                      page: Int = 1,
                      limit: Int = 20) async {
        isLoading = true
        errorMessage = nil

        let queryParams = UserLogQueryParams(
            userId: userId,
            recordType: recordType,
            isPublic: isPublic,
            startDate: startDate,
            endDate: endDate,
            page: page,
            limit: limit
        )

        currentQueryParams = queryParams

        do {
            let logListData = try await userLogManager.fetchUserLogs(queryParams)

            // 如果是第一页，替换数据；否则追加数据
            if page == 1 {
                userLogs = logListData.logs
            } else {
                userLogs.append(contentsOf: logListData.logs)
            }

            paginationInfo = logListData.pagination
            print("✅ 用户日志获取成功，当前共 \(userLogs.count) 条记录")

        } catch {
            let logError = mapError(error)
            errorMessage = logError.localizedDescription
            print("❌ 获取用户日志失败: \(logError.localizedDescription)")
        }

        isLoading = false
    }

    /// 加载更多日志
    func loadMoreLogs() async {
        guard let currentParams = currentQueryParams,
              let pagination = paginationInfo,
              pagination.hasNext,
              !isLoadingMore else {
            return
        }

        isLoadingMore = true

        let nextPage = pagination.current + 1
        await fetchUserLogs(
            userId: currentParams.userId,
            recordType: currentParams.recordType,
            isPublic: currentParams.isPublic,
            page: nextPage,
            limit: currentParams.limit ?? 20
        )

        isLoadingMore = false
    }

    /// 刷新日志列表
    func refreshLogs() async {
        guard let currentParams = currentQueryParams else {
            return
        }

        await fetchUserLogs(
            userId: currentParams.userId,
            recordType: currentParams.recordType,
            isPublic: currentParams.isPublic,
            page: 1,
            limit: currentParams.limit ?? 20
        )
    }

    /// 应用筛选条件
    func applyFilters(userId: String) async {
        await fetchUserLogs(
            userId: userId,
            recordType: selectedRecordType,
            isPublic: showPublicOnly ? true : nil,
            page: 1,
            limit: 20
        )
    }

    // MARK: - 日志管理方法

    /// 创建用户日志
    func createUserLog(userId: String,
                      recordType: RecordType,
                      recordId: String,
                      imageList: [String]? = nil,
                      description: String? = nil,
                      isPublic: Bool = true) async {
        isLoading = true
        errorMessage = nil
        successMessage = nil

        let request = CreateUserLogRequest(
            userId: userId,
            recordType: recordType,
            recordId: recordId,
            imageList: imageList,
            description: description,
            isPublic: isPublic
        )

        do {
            let newLog = try await userLogManager.createUserLog(request)

            // 将新日志添加到列表顶部
            userLogs.insert(newLog, at: 0)
            successMessage = "日志创建成功"
            print("✅ 用户日志创建成功: \(newLog.id)")

        } catch {
            let logError = mapError(error)
            errorMessage = logError.localizedDescription
            print("❌ 创建用户日志失败: \(logError.localizedDescription)")
        }

        isLoading = false
    }

    /// 更新用户日志
    func updateUserLog(logId: String,
                      imageList: [String]? = nil,
                      description: String? = nil,
                      isPublic: Bool? = nil) async {
        isLoading = true
        errorMessage = nil
        successMessage = nil

        let request = UpdateUserLogRequest(
            logId: logId,
            imageList: imageList,
            description: description,
            isPublic: isPublic
        )

        do {
            let updatedLog = try await userLogManager.updateUserLog(request)

            // 更新列表中的日志
            if let index = userLogs.firstIndex(where: { $0.id == logId }) {
                userLogs[index] = updatedLog
            }

            successMessage = "日志更新成功"
            print("✅ 用户日志更新成功: \(updatedLog.id)")

        } catch {
            let logError = mapError(error)
            errorMessage = logError.localizedDescription
            print("❌ 更新用户日志失败: \(logError.localizedDescription)")
        }

        isLoading = false
    }

    /// 删除用户日志
    func deleteUserLog(logId: String, userId: String) async {
        isLoading = true
        errorMessage = nil
        successMessage = nil

        do {
            try await userLogManager.deleteUserLog(logId: logId, userId: userId)

            // 从列表中移除日志
            userLogs.removeAll { $0.id == logId }
            successMessage = "日志删除成功"
            print("✅ 用户日志删除成功: \(logId)")

        } catch {
            let logError = mapError(error)
            errorMessage = logError.localizedDescription
            print("❌ 删除用户日志失败: \(logError.localizedDescription)")
        }

        isLoading = false
    }

    // MARK: - 点赞管理方法

    /// 点赞日志
    func likeLog(logId: String, userId: String) async {
        errorMessage = nil
        successMessage = nil

        let request = CreateLikeRequest(logId: logId, userId: userId)

        do {
            let newLike = try await logDetailManager.createLike(request)

            // 更新本地日志的点赞列表
            if let index = userLogs.firstIndex(where: { $0.id == logId }) {
                var updatedLog = userLogs[index]
                var likes = updatedLog.likes ?? []
                likes.append(newLike)

                // 创建新的UserLog实例（因为UserLog是struct）
                let newLog = UserLog(
                    id: updatedLog.id,
                    userId: updatedLog.userId,
                    recordType: updatedLog.recordType,
                    recordId: updatedLog.recordId,
                    imageList: updatedLog.imageList,
                    description: updatedLog.description,
                    isPublic: updatedLog.isPublic,
                    createdAt: updatedLog.createdAt,
                    updatedAt: updatedLog.updatedAt,
                    user: updatedLog.user,
                    likes: likes,
                    comments: updatedLog.comments,
                    locationCheckIns: updatedLog.locationCheckIns,
                    userFootprints: updatedLog.userFootprints,
                    cardAcquisitionRecord: updatedLog.cardAcquisitionRecord
                )

                userLogs[index] = newLog
            }

            print("✅ 点赞成功: \(newLike.id)")

        } catch {
            let logError = mapError(error)
            errorMessage = logError.localizedDescription
            print("❌ 点赞失败: \(logError.localizedDescription)")
        }
    }

    /// 取消点赞
    func unlikeLog(logId: String, userId: String) async {
        errorMessage = nil
        successMessage = nil

        do {
            try await logDetailManager.deleteLike(logId: logId, userId: userId)

            // 更新本地日志的点赞列表
            if let index = userLogs.firstIndex(where: { $0.id == logId }) {
                var updatedLog = userLogs[index]
                let likes = updatedLog.likes?.filter { $0.userId != userId } ?? []

                // 创建新的UserLog实例
                let newLog = UserLog(
                    id: updatedLog.id,
                    userId: updatedLog.userId,
                    recordType: updatedLog.recordType,
                    recordId: updatedLog.recordId,
                    imageList: updatedLog.imageList,
                    description: updatedLog.description,
                    isPublic: updatedLog.isPublic,
                    createdAt: updatedLog.createdAt,
                    updatedAt: updatedLog.updatedAt,
                    user: updatedLog.user,
                    likes: likes,
                    comments: updatedLog.comments,
                    locationCheckIns: updatedLog.locationCheckIns,
                    userFootprints: updatedLog.userFootprints, 
                    cardAcquisitionRecord: updatedLog.cardAcquisitionRecord
                )

                userLogs[index] = newLog
            }

            print("✅ 取消点赞成功")

        } catch {
            let logError = mapError(error)
            errorMessage = logError.localizedDescription
            print("❌ 取消点赞失败: \(logError.localizedDescription)")
        }
    }

    /// 检查用户是否已点赞
    func isLogLikedByUser(logId: String, userId: String) -> Bool {
        guard let log = userLogs.first(where: { $0.id == logId }),
              let likes = log.likes else {
            return false
        }

        return likes.contains { $0.userId == userId }
    }

    /// 获取日志点赞数
    func getLikeCount(for logId: String) -> Int {
        guard let log = userLogs.first(where: { $0.id == logId }) else {
            return 0
        }

        return log.likes?.count ?? 0
    }

    // MARK: - 评论管理方法

    /// 添加评论
    func addComment(logId: String,
                   userId: String,
                   content: String,
                   replyTo: String? = nil) async {
        errorMessage = nil
        successMessage = nil

        let request = CreateCommentRequest(
            logId: logId,
            userId: userId,
            content: content,
            replyTo: replyTo
        )

        do {
            let newComment = try await logDetailManager.createComment(request)

            // 更新本地日志的评论列表
            if let index = userLogs.firstIndex(where: { $0.id == logId }) {
                var updatedLog = userLogs[index]
                var comments = updatedLog.comments ?? []
                comments.append(newComment)

                // 创建新的UserLog实例
                let newLog = UserLog(
                    id: updatedLog.id,
                    userId: updatedLog.userId,
                    recordType: updatedLog.recordType,
                    recordId: updatedLog.recordId,
                    imageList: updatedLog.imageList,
                    description: updatedLog.description,
                    isPublic: updatedLog.isPublic,
                    createdAt: updatedLog.createdAt,
                    updatedAt: updatedLog.updatedAt,
                    user: updatedLog.user,
                    likes: updatedLog.likes,
                    comments: comments,
                    locationCheckIns: updatedLog.locationCheckIns,
                    userFootprints: updatedLog.userFootprints,
                    cardAcquisitionRecord: updatedLog.cardAcquisitionRecord
                )

                userLogs[index] = newLog
            }

            successMessage = "评论添加成功"
            print("✅ 评论添加成功: \(newComment.id)")

        } catch {
            let logError = mapError(error)
            errorMessage = logError.localizedDescription
            print("❌ 添加评论失败: \(logError.localizedDescription)")
        }
    }

    /// 删除评论
    func deleteComment(commentId: String, userId: String, logId: String) async {
        errorMessage = nil
        successMessage = nil

        do {
            try await logDetailManager.deleteComment(commentId: commentId, userId: userId)

            // 更新本地日志的评论列表
            if let index = userLogs.firstIndex(where: { $0.id == logId }) {
                var updatedLog = userLogs[index]
                let comments = updatedLog.comments?.filter { $0.id != commentId } ?? []

                // 创建新的UserLog实例
                let newLog = UserLog(
                    id: updatedLog.id,
                    userId: updatedLog.userId,
                    recordType: updatedLog.recordType,
                    recordId: updatedLog.recordId,
                    imageList: updatedLog.imageList,
                    description: updatedLog.description,
                    isPublic: updatedLog.isPublic,
                    createdAt: updatedLog.createdAt,
                    updatedAt: updatedLog.updatedAt,
                    user: updatedLog.user,
                    likes: updatedLog.likes,
                    comments: comments,
                    locationCheckIns: updatedLog.locationCheckIns,
                    userFootprints: updatedLog.userFootprints, 
                    cardAcquisitionRecord: updatedLog.cardAcquisitionRecord
                )

                userLogs[index] = newLog
            }

            successMessage = "评论删除成功"
            print("✅ 评论删除成功")

        } catch {
            let logError = mapError(error)
            errorMessage = logError.localizedDescription
            print("❌ 删除评论失败: \(logError.localizedDescription)")
        }
    }

    /// 获取日志评论数
    func getCommentCount(for logId: String) -> Int {
        guard let log = userLogs.first(where: { $0.id == logId }) else {
            return 0
        }

        return log.comments?.count ?? 0
    }

    /// 获取日志的评论列表
    func getComments(for logId: String) -> [LogComment] {
        guard let log = userLogs.first(where: { $0.id == logId }) else {
            return []
        }

        return log.comments ?? []
    }

    // MARK: - 辅助方法

    /// 根据ID获取日志
    func getLog(by id: String) -> UserLog? {
        return userLogs.first { $0.id == id }
    }

    /// 根据记录类型筛选日志
    func getLogsByRecordType(_ recordType: RecordType) -> [UserLog] {
        return userLogs.filter { $0.recordType == recordType }
    }

    /// 获取公开日志
    func getPublicLogs() -> [UserLog] {
        return userLogs.filter { $0.isPublic }
    }

    /// 获取私有日志
    func getPrivateLogs() -> [UserLog] {
        return userLogs.filter { !$0.isPublic }
    }

    /// 清除所有数据
    func clearAllData() {
        userLogs.removeAll()
        paginationInfo = nil
        currentQueryParams = nil
        clearMessages()
    }

    /// 清除消息
    func clearMessages() {
        errorMessage = nil
        successMessage = nil
    }

    /// 重置筛选条件
    func resetFilters() {
        selectedRecordType = nil
        showPublicOnly = true
    }

    // MARK: - Private Methods

    /// 映射错误类型
    private func mapError(_ error: Error) -> UserLogError {
        if let userLogError = error as? UserLogError {
            return userLogError
        }

        if error is URLError {
            return .networkError(error)
        }

        if error is DecodingError {
            return .decodingError
        }

        return .responseError(error.localizedDescription)
    }
}

// MARK: - 便捷扩展

extension LogViewModel {

    /// 是否有更多数据可加载
    var hasMoreData: Bool {
        return paginationInfo?.hasNext ?? false
    }

    /// 当前页码
    var currentPage: Int {
        return paginationInfo?.current ?? 1
    }

    /// 总记录数
    var totalCount: Int {
        return paginationInfo?.count ?? 0
    }

    /// 是否为空列表
    var isEmpty: Bool {
        return userLogs.isEmpty
    }

    /// 获取记录类型的显示名称
    func getRecordTypeDisplayName(_ recordType: RecordType) -> String {
        return recordType.displayName
    }
}
