//
//  ItemCardSheetView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/31.
//

import SwiftUI
import Foundation

// MARK: - 卡片Sheet视图
struct ItemCardSheetView: View {
    // MARK: - Properties
    @EnvironmentObject private var cardStore: CardStore
    @StateObject private var userItemCardViewModel = UserItemCardViewModel()
    @State private var sheetHeight: CGFloat = UIScreen.main.bounds.height / 4 // 初始高度
    @State private var isDragging = false
    @State private var draggedUserItemCard: UserItemCard?
    @AppStorage("currentUserId") private var currentUserId: String = ""

    // 屏幕尺寸
    private let screenHeight = UIScreen.main.bounds.height
    private let fullHeight: CGFloat
    private let compactHeight: CGFloat

    // 拖拽处理回调
    let onCardDropped: (String, String) -> Void // (cardId, userId)
    let onGlobalDragStarted: ((ItemCard, CGPoint) -> Void)?
    let onGlobalDragChanged: ((CGPoint) -> Void)?
    let onGlobalDragEnded: (() -> Void)?

    init(
        onCardDropped: @escaping (String, String) -> Void,
        onGlobalDragStarted: ((ItemCard, CGPoint) -> Void)? = nil,
        onGlobalDragChanged: ((CGPoint) -> Void)? = nil,
        onGlobalDragEnded: (() -> Void)? = nil
    ) {
        self.onCardDropped = onCardDropped
        self.onGlobalDragStarted = onGlobalDragStarted
        self.onGlobalDragChanged = onGlobalDragChanged
        self.onGlobalDragEnded = onGlobalDragEnded
        self.fullHeight = UIScreen.main.bounds.height / 4
        self.compactHeight = UIScreen.main.bounds.height / 6
    }

    var body: some View {
        VStack(spacing: 0) {
            // 拖拽指示器
            dragIndicator

            // 标题栏
            headerView

            // 卡片滚动视图
            cardScrollView

            Spacer()
        }
        .frame(height: sheetHeight)
        .frame(width: UIScreen.main.bounds.width * 0.95)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.thinMaterial)
                .ignoresSafeArea(edges: .bottom)
        )
        .animation(.easeInOut(duration: 0.3), value: sheetHeight)
        .onAppear {
            // 加载用户卡片数据
            Task {
                await userItemCardViewModel.fetchUserItemCards(for: currentUserId)
            }
        }
    }

    // MARK: - 拖拽指示器
    private var dragIndicator: some View {
        RoundedRectangle(cornerRadius: 2)
            .fill(Color.textSecondary.opacity(0.5))
            .frame(width: 40, height: 4)
            .padding(.top, Theme.Spacing.sm)
    }

    // MARK: - 标题栏
    private var headerView: some View {
        HStack {
            Text("我的卡片")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            Spacer()

            Text("\(userItemCardViewModel.userItemCards.count) 张")
                .font(.captionBrand)
                .foregroundColor(.textSecondary)
        }
        .padding(.horizontal, Theme.Spacing.md)
        .padding(.vertical, Theme.Spacing.sm)
    }

    // MARK: - 卡片滚动视图
    private var cardScrollView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            LazyHStack(spacing: Theme.Spacing.md) {
                ForEach(userItemCardViewModel.userItemCards) { userItemCard in
                    DraggableUserItemCardThumbnail(
                        userItemCard: userItemCard,
                        onDragStarted: handleDragStarted,
                        onDragChanged: handleDragChanged,
                        onDragEnded: handleDragEnded
                    )
                }
            }
            .padding(.horizontal, Theme.Spacing.md)
        }
        .frame(height: 160)
    }

    // MARK: - 拖拽处理方法

    /// 处理拖拽开始
    private func handleDragStarted(_ userItemCard: UserItemCard) {
        draggedUserItemCard = userItemCard
        isDragging = true
        print("🎯 开始拖拽卡片: \(userItemCard.card?.title ?? "未知卡片")")

        // 触发全局拖拽开始
        if let onGlobalDragStarted = onGlobalDragStarted,
           let itemCard = userItemCard.card {
            // 计算卡片在屏幕中的初始位置
            let screenCenter = CGPoint(x: UIScreen.main.bounds.width / 2, y: UIScreen.main.bounds.height * 0.8)
            onGlobalDragStarted(itemCard, screenCenter)
        }
    }

    /// 处理拖拽状态变化
    private func handleDragChanged(_ userItemCard: UserItemCard, _ value: DragGesture.Value) {
        draggedUserItemCard = userItemCard
        isDragging = true

        // 更新全局拖拽位置
        if let onGlobalDragChanged = onGlobalDragChanged {
            let globalPosition = CGPoint(
                x: value.location.x,
                y: value.location.y
            )
            onGlobalDragChanged(globalPosition)
        }

        // TODO: 检查是否拖拽出sheet边界
//        let dragY = value.translation.height
//        print("dragY: \(dragY)")
//
//        if dragY < -50 { // 向上拖拽超过50点
//            // 缩小sheet高度
//            withAnimation(.easeInOut(duration: 0.3)) {
//                sheetHeight = compactHeight
//            }
//        } else if dragY > -20 && sheetHeight == compactHeight {
//            // 拖拽回sheet区域，恢复高度
//            withAnimation(.easeInOut(duration: 0.3)) {
//                sheetHeight = fullHeight
//            }
//        }
    }

    /// 处理拖拽结束
    private func handleDragEnded(_ userItemCard: UserItemCard, _ value: DragGesture.Value) {
        isDragging = false
        draggedUserItemCard = nil

        // 触发全局拖拽结束
        onGlobalDragEnded?()

        // 恢复sheet高度
//        withAnimation(.easeInOut(duration: 0.3)) {
//            sheetHeight = fullHeight
//        }
    }
}

// MARK: - 可拖拽用户卡片缩略图
struct DraggableUserItemCardThumbnail: View {
    let userItemCard: UserItemCard
    let onDragStarted: (UserItemCard) -> Void
    let onDragChanged: (UserItemCard, DragGesture.Value) -> Void
    let onDragEnded: (UserItemCard, DragGesture.Value) -> Void

    @State private var dragOffset = CGSize.zero
    @State private var isPressed = false
    @State private var isDragging = false
    @State private var showCardDetail = false
    @GestureState private var gestureState: DragState = .inactive

    // 便利属性，用于访问卡片信息
    private var card: ItemCard {
        userItemCard.card ?? ItemCard(
            id: "placeholder",
            cardType: .scenery,
            themeColor: nil,
            coinReward: 0,
            experienceReward: 0,
            description: "无法加载卡片信息",
            title: "错误",
            imageFileName: "",
            imageURL: "",
            createdAt: Date(),
            authorId: "",
            location: "",
            latitude: nil,
            longitude: nil
        )
    }

    // 拖拽状态枚举
    enum DragState {
        case inactive
        case longPressing
        case dragging(translation: CGSize)

        var translation: CGSize {
            switch self {
            case .dragging(let translation):
                return translation
            default:
                return .zero
            }
        }

        var isActive: Bool {
            switch self {
            case .inactive:
                return false
            default:
                return true
            }
        }
    }

    var body: some View {
        cardContent
            .onTapGesture {
                // 只有在非拖拽状态下才允许显示详情
                if !gestureState.isActive && !isDragging {
                    showCardDetail = true
                }
            }
            .sheet(isPresented: $showCardDetail) {
                cardDetailSheet(userItemCard)
            }
    }

    // MARK: - 卡片内容视图
    private var cardContent: some View {
        DragItemCardView(userItemCard: userItemCard)
            .scaleEffect(gestureState.isActive ? 1.05 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: gestureState.isActive)
            .onDrag {
                // 系统级拖拽支持 - 提供带前缀的卡片ID作为拖拽数据
                return NSItemProvider(object: DragManager.createDraggableCardId(card.id) as NSString)
            }
            .simultaneousGesture(
                // 使用 simultaneousGesture 避免与点击手势冲突
                LongPressGesture(minimumDuration: 0.5)
                    .onEnded { _ in
                        // 长按开始拖拽模式
                        isDragging = true
                        onDragStarted(userItemCard)
                    }
            )
            .simultaneousGesture(
                DragGesture(coordinateSpace: .global)
                    .onChanged { value in
                        if isDragging {
                            onDragChanged(userItemCard, value)
                        }
                    }
                    .onEnded { value in
                        if isDragging {
                            onDragEnded(userItemCard, value)
                            isDragging = false
                        }
                    }
            )
    }


}

// MARK: - 预览
#Preview("卡片Sheet视图") {
    let sampleCard1 = ItemCard(
        id: "card1",
        cardType: .shopping,
        themeColor: "4B7905",
        coinReward: 15,
        experienceReward: 8,
        description: "这是一个环保购物袋",
        title: "环保购物袋",
        imageFileName: "",
        imageURL: "",
        createdAt: Date(),
        authorId: "user1",
        location: "北京市",
        latitude: 39.9042,
        longitude: 116.4074
    )

    let sampleCard2 = ItemCard(
        id: "card2",
        cardType: .scenery,
        themeColor: nil,
        coinReward: 10,
        experienceReward: 5,
        description: "这是一个美丽的风景",
        title: "公园风景",
        imageFileName: "",
        imageURL: "",
        createdAt: Date(),
        authorId: "user1",
        location: "上海市",
        latitude: 31.2304,
        longitude: 121.4737
    )

    let cardStore = CardStore()

    return ItemCardSheetView { cardId, userId in
        print("卡片 \(cardId) 拖拽到用户 \(userId)")
    }
    .environmentObject(cardStore)
    .background(Color.black)
}
