//
//  MapView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/20.
//

import SwiftUI
import MapKit
import CoreLocation
import PopupView

/// 为系统类型 CLLocationCoordinate2D 添加 Equatable 扩展
extension CLLocationCoordinate2D: @retroactive Equatable {
    public static func == (lhs: CLLocationCoordinate2D, rhs: CLLocationCoordinate2D) -> Bool {
        lhs.latitude == rhs.latitude && lhs.longitude == rhs.longitude
    }
}

// MARK: - 地图视图组件
struct MapView: View {
    @StateObject private var viewModel: MapViewModel
    @StateObject private var friendMapViewModel: FriendMapViewModel
    @StateObject private var cardTransferViewModel = CardTransferViewModel()
    @StateObject private var propInteractionViewModel : PropInteractionViewModel
    
    // 获取当前用户Id
    @AppStorage("currentUserId") private var currentUserId: String = ""
    
    @EnvironmentObject private var appSettings: AppSettings
    @Environment(\.dismiss) private var dismiss
    @State private var showLocationPermissionAlert = false
    
    // MARK: 卡片和道具显示状态
    @State private var isAnimating = false
    @State private var refreshTask: Task<Void, Never>?
    @State private var showCardOverlay = false
    
    
    // MARK: - 卡片传输状态
    @State private var isDraggingCard = false
    @State private var draggedCard: ItemCard?
    @State private var dragPosition: CGPoint = .zero
    @State private var dragStartPosition: CGPoint = .zero
    @State private var showTransferSheet = false
    
    /// 用于绑定自定义的alert
    @State private var showDropAlert = false
    /// 存储待传输的卡片ID
    @State private var pendingCardId: String?
    /// 存储待传输的接收者ID
    @State private var pendingReceiverId: String?
    
    // MARK: - 道具传输状态
    @State private var isDraggingProp = false
    @State private var draggedProp: PropInfo?
    @State private var propDragPosition: CGPoint = .zero
    @State private var propDragStartPosition: CGPoint = .zero
    @State private var showPropOverlay = false
    /// 存储待传输的道具ID
    @State private var pendingPropId: Int?
    /// 存储待传输的接收者ID
    @State private var pendingPropReceiverId: String?
    
    // MARK: - 地点打卡相关状态
    @StateObject private var placeCheckinViewModel = PlaceCheckinViewModel()
    @State private var showCheckinSheet = false
    @State private var showFootprintSheet = false
    @State private var showCheckinAnnotations = true
    // MARK: - 选中好友状态管理
    // MARK: - 选中好友状态管理
    /// 当前选中的好友ID（用于背景变暗和头像高亮效果）
    @State private var selectedFriendId: String?
    /// 是否显示背景遮罩（当有sheet弹出时）
    @State private var showBackgroundOverlay = false

    /// 当前选中的好友ID（用于背景变暗和头像高亮效果）
    @State private var selectedFriendId: String?
    /// 是否显示背景遮罩（当有sheet弹出时）
    @State private var showBackgroundOverlay = false
    
    // MARK: - 选中好友状态管理
    /// 当前选中的好友ID（用于背景变暗和头像高亮效果）
    @State private var selectedFriendId: String?
    /// 是否显示背景遮罩（当有sheet弹出时）
    @State private var showBackgroundOverlay = false
    // MARK: - 选中好友状态管理
    /// 当前选中的好友ID（用于背景变暗和头像高亮效果）
    @State private var selectedFriendId: String?
    /// 是否显示背景遮罩（当有sheet弹出时）
    @State private var showBackgroundOverlay = false

    init() {
        self._viewModel = StateObject(wrappedValue: MapViewModel())
        self._friendMapViewModel = StateObject(wrappedValue: FriendMapViewModel())
        
        // 使用当前用户Id初始化道具管理viewmodel
        let userId = UserDefaults.standard.string(forKey: "currentUserId") ?? ""
        self._propInteractionViewModel = StateObject(wrappedValue: PropInteractionViewModel(currentUserId: userId))
    }
    
    // MARK: Body
    var body: some View {
        NavigationStack {
            ZStack {
                mapContent
                
                topButtonSection
                
                BottomControlsView(
                    viewModel: viewModel,
                    friendMapViewModel: friendMapViewModel,
                    showCheckinSheet: $showCheckinSheet,
                    showFootprintSheet: $showFootprintSheet
                )
            }
            .overlay(alignment: .bottom) {
                // 卡片覆盖层视图
                if showCardOverlay {
                    Spacer()
                    
                    ItemCardSheetView(
                        onCardDropped: { cardId, userId in
                            handleCardDropToFriend(cardId: cardId, userId: userId)
                        },
                        onGlobalDragStarted: { card, position in
                            startGlobalDrag(card: card, startPosition: position)
                        },
                        onGlobalDragChanged: { position in
                            updateGlobalDrag(to: position)
                        },
                        onGlobalDragEnded: {
                            endGlobalDrag()
                        }
                    )
                    .background(Color.clear.ignoresSafeArea(edges: .bottom))
                    .environmentObject(CardStore())
                    .frame(maxHeight: UIScreen.main.bounds.height / 4)
                    .transition(.move(edge: .bottom))
                    .animation(.easeInOut(duration: 0.3), value: showCardOverlay)
                }
                
                // 道具覆盖层视图
                if showPropOverlay {
                    Spacer()
                    
                    PropTransferSheet(
                        onPropDropped: { propId, userId in
                            handlePropDropToFriend(propId: propId, userId: userId)
                        },
                        onGlobalDragStarted: { prop, position in
                            startGlobalPropDrag(prop: prop, startPosition: position)
                        },
                        onGlobalDragChanged: { position in
                            updateGlobalPropDrag(to: position)
                        },
                        onGlobalDragEnded: {
                            endGlobalPropDrag()
                        }
                    )
                    .background(Color.clear.ignoresSafeArea(edges: .bottom))
                    .frame(maxHeight: UIScreen.main.bounds.height / 4)
                    .transition(.move(edge: .bottom))
                    .animation(.easeInOut(duration: 0.3), value: showPropOverlay)
                }
            }
            .toolbar {
                ToolbarItemGroup(placement: .bottomBar) {
                    // 传输按钮
                    Button {
                        showTransferSheet = true
                    } label: {
                        HStack {
                            Image(systemName: "arrow.left.arrow.right")
                                .font(.title3)
                            Text("传输")
                                .font(.captionBrand)
                        }
                        .foregroundColor(.brandGreen)
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .alert("位置权限", isPresented: $showLocationPermissionAlert) {
            Button("去设置") {
                if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(settingsUrl)
                }
            }
            Button("取消", role: .cancel) { }
        } message: {
            Text(viewModel.errorMessage ?? "需要位置权限才能显示您的当前位置")
        }
        .alert("好友位置", isPresented: .constant(friendMapViewModel.errorMessage != nil)) {
            Button("确定") {
                friendMapViewModel.clearMessages()
            }
        } message: {
            if let errorMessage = friendMapViewModel.errorMessage {
                Text(errorMessage)
            }
        }
        .onAppear {
            viewModel.startLocationUpdates()
            isAnimating = true
            
            // 立即获取好友位置信息
            Task{
                await friendMapViewModel.loadFriendLocations(for: appSettings.userId)
            }
            
            // 加载地点打卡记录
            Task {
                await placeCheckinViewModel.loadCheckinsList(userId: appSettings.userId)
            }
            
            // 启动道具交互消息轮询
            propInteractionViewModel.startPollingUnreadMessages()
            
            // 定时更新好友位置信息
            refreshTask = Task {
                while !Task.isCancelled {
                    try? await Task.sleep(for: .seconds(60))
                    await friendMapViewModel.refreshFriendLocations(for: appSettings.userId)
                    print("定时自动更新好友位置信息")
                }
            }
        }
        .onDisappear {
            viewModel.stopLocationUpdates()
            refreshTask?.cancel()
            refreshTask = nil
            // 停止道具交互消息轮询
            propInteractionViewModel.stopPollingUnreadMessages()
        }
        .onChange(of: viewModel.errorMessage) { _, newValue in
            if newValue != nil {
                showLocationPermissionAlert = true
            }
        }
        .sheet(isPresented: $showTransferSheet) {
            CardTransferSheet()
        }
        .sheet(isPresented: $showCheckinSheet){
            VStack{
                CheckinSheet()
            }
            .presentationDetents([.height(400), .fraction(0.95)])
            .presentationCornerRadius(40)
            .presentationDragIndicator(.hidden)
        }
        .sheet(isPresented: $showFootprintSheet){
            FootprintSheet()
                .presentationDetents([.fraction(0.95)])
                .presentationCornerRadius(40)
                .presentationDragIndicator(.hidden)
        }
        .alert("传输成功", isPresented: .constant(cardTransferViewModel.successMessage != nil)) {
            Button("确定") {
                cardTransferViewModel.clearMessages()
            }
        } message: {
            if let successMessage = cardTransferViewModel.successMessage {
                Text(successMessage)
            }
        }
        .alert("传输失败", isPresented: .constant(cardTransferViewModel.errorMessage != nil)) {
            Button("确定") {
                cardTransferViewModel.clearMessages()
            }
        } message: {
            if let errorMessage = cardTransferViewModel.errorMessage {
                Text(errorMessage)
            }
        }
        // MARK: 自定义确认弹窗
        .overlay{
            CustomAlert(
                isPresented: $showDropAlert,
                title: pendingPropId != nil ? "确认分享道具" : "确认分享卡片",
                message: "您确定要将\(pendingPropId != nil ? "道具" : "卡片")分享给 \(friendMapViewModel.friendAnnotations.first(where: { $0.friendLocation.userId == (pendingPropId != nil ? pendingPropReceiverId : pendingReceiverId) })?.friendLocation.nickname ?? "该好友") 吗？",
                confirmTitle:"分享",
                cancelTitle:"再想想",
                onConfirm: {
                    if pendingPropId != nil {
                        confirmPropDropToFriend()
                    } else {
                        confirmCardDropToFriend()
                    }
                },
                onCancel: {
                    // 用户取消传输，清除待传输信息
                    if pendingPropId != nil {
                        pendingPropId = nil
                        pendingPropReceiverId = nil
                    } else {
                        pendingCardId = nil
                        pendingReceiverId = nil
                    }
                },
            )
        }
        // MARK: 道具消息弹窗
        .popup(isPresented: $propInteractionViewModel.showNewMessagePopup) {
            if let latestMessage = propInteractionViewModel.latestUnreadMessage {
                PopupPropSheet(interaction: latestMessage)
            }
        } customize: {
            $0.position(.center)
                .closeOnTap(true)
                .backgroundColor(.black.opacity(0.4))
                .dismissCallback{
                    Task{
                        if let latestMessage = propInteractionViewModel.latestUnreadMessage{
                            await propInteractionViewModel.markAsRead(interactionId: latestMessage.id)
                        }
                    }
                    
                    propInteractionViewModel.showNewMessagePopup = false
                }
        }
    }
    
    // MARK: - 全局拖拽处理函数
    
    /// 开始全局拖拽
    private func startGlobalDrag(card: ItemCard, startPosition: CGPoint) {
        isDraggingCard = true
        draggedCard = card
        dragStartPosition = startPosition
        dragPosition = startPosition
        print("🎯 开始全局拖拽: \(card.title)")
    }
    
    /// 更新拖拽位置
    private func updateGlobalDrag(to position: CGPoint) {
        dragPosition = position
    }
    
    /// 结束全局拖拽
    private func endGlobalDrag() {
        isDraggingCard = false
        draggedCard = nil
        dragPosition = .zero
        dragStartPosition = .zero
    }
    
    // MARK: - 道具全局拖拽处理函数
    
    /// 开始道具全局拖拽
    private func startGlobalPropDrag(prop: PropInfo, startPosition: CGPoint) {
        isDraggingProp = true
        draggedProp = prop
        propDragStartPosition = startPosition
        propDragPosition = startPosition
        print("🎯 开始全局拖拽道具: \(prop.propTitle)")
    }
    
    /// 更新道具拖拽位置
    private func updateGlobalPropDrag(to position: CGPoint) {
        propDragPosition = position
    }
    
    /// 结束道具全局拖拽
    private func endGlobalPropDrag() {
        isDraggingProp = false
        draggedProp = nil
        propDragPosition = .zero
        propDragStartPosition = .zero
    }
    
    
    // MARK: - 卡片拖放处理函数
    
    /// 处理卡片拖放到好友位置
    private func handleCardDropToFriend(cardId: String, userId: String) {
        print("🎯 卡片 \(cardId) 被拖放到好友 \(userId)")
        
        // 保存待传输信息
        pendingCardId = cardId
        pendingReceiverId = userId
        
        // 显示确认弹窗
        showDropAlert = true
    }
    
    /// 确认卡片拖放到好友位置并执行传输
    private func confirmCardDropToFriend() {
        guard let cardId = pendingCardId, let userId = pendingReceiverId else {
            print("❌ 缺少卡片或接收者信息，无法传输。")
            return
        }
        
        // 获取好友信息用于显示
        let friendName = friendMapViewModel.friendAnnotations.first(where: { $0.friendLocation.userId == userId })?.friendLocation.nickname ?? "未知好友"
        
        // 调用传输API
        Task {
            await cardTransferViewModel.createCardTransfer(cardId: cardId, receiverId: userId)
        }
        
        print("✅ 卡片传输请求已发送给 \(friendName)")
        
        // 关闭卡片覆盖层
        withAnimation(.easeInOut(duration: 0.3)) {
            showCardOverlay = false
        }
        
        // 清除待传输信息
        pendingCardId = nil
        pendingReceiverId = nil
    }
    
    // MARK: - 道具拖放处理函数
    
    /// 处理道具拖放到好友位置
    private func handlePropDropToFriend(propId: Int, userId: String) {
        print("🎯 道具 \(propId) 被拖放到好友 \(userId)")
        
        // 保存待传输信息
        pendingPropId = propId
        pendingPropReceiverId = userId
        
        // 显示确认弹窗（这里可以复用现有的弹窗或创建新的）
        showDropAlert = true
    }
    
    /// 确认道具拖放到好友位置并执行传输
    private func confirmPropDropToFriend() {
        guard let propId = pendingPropId, let userId = pendingPropReceiverId else {
            print("❌ 缺少道具或接收者信息，无法传输。")
            return
        }
        
        // 获取好友信息用于显示
        let friendName = friendMapViewModel.friendAnnotations.first(where: { $0.friendLocation.userId == userId })?.friendLocation.nickname ?? "未知好友"
        
        // 调用道具传输API
        Task {
            await propInteractionViewModel.createPropInteraction(receiverUserId: userId, propId: propId, remark: "")
        }
        
        print("✅ 道具传输请求已发送给 \(friendName)")
        
        // 关闭道具覆盖层
        withAnimation(.easeInOut(duration: 0.3)) {
            showPropOverlay = false
        }
        
        // 清除待传输信息
        pendingPropId = nil
        pendingPropReceiverId = nil
    }
    
    // MARK: 拆分视图
    
    // 地图主体
    private var mapContent: some View {
        Map(position: $viewModel.cameraPosition) {
            // 用户当前位置标记
            if let userLocation = viewModel.userLocation {
                Annotation("我在这里", coordinate: userLocation, anchor: .center) {
                    ZStack {
                        // 外圈脉冲效果
                        Circle()
                            .fill(Color.brandGreen.opacity(0.3))
                            .frame(width: 40, height: 40)
                            .scaleEffect(1.5)
                            .opacity(0.6)
                            .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: userLocation)
                        // TODO: loop的动画：伴随scale和easeOut效果
                        
                        // 内圈位置标记
                        if let userHeading = viewModel.userHeading{
                            Image(systemName: "location.fill")
                                .foregroundColor(.white)
                                .font(.title2)
                                .padding(8)
                                .rotationEffect(Angle(degrees: userHeading))
                                .background(
                                    Circle()
                                        .fill(Color.brandGreen)
                                        .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
                                )
                        }
                    }
                }
            }
            
            // 好友位置标注
            ForEach(friendMapViewModel.friendAnnotations) { annotation in
                Annotation(
                    annotation.title,
                    coordinate: annotation.coordinate,
                    anchor: .center
                ) {
                    FriendOnMapView(
                        friendLocation: annotation.friendLocation,
                        avatarSize: 45,
                        showDetails: true,
                        onCardDropped: { cardId in
                            handleCardDropToFriend(cardId: cardId, userId: annotation.friendLocation.userId)
                        },
                        onPropDropped: { propId in
                            handlePropDropToFriend(propId: propId, userId: annotation.friendLocation.userId)
                        }
                    )
                    .onTapGesture {
                        // 点击好友标注时的交互逻辑
                        print("🗺️ 点击了好友: \(annotation.friendLocation.nickname)")
                    }
                }
            }
            
            // 地点打卡标注
            if showCheckinAnnotations {
                ForEach(placeCheckinViewModel.checkinsList) { checkin in
                    Annotation(
                        checkin.displayName.count > 7 ? String(checkin.displayName.prefix(7)) + "..." : checkin.displayName,
                        coordinate: checkin.coordinate,
                        anchor: .center
                    ) {
                        CheckinAnnotationView(checkin: checkin)
                            .onTapGesture {
                                print("📍 点击了打卡点: \(checkin.displayName)")
                            }
                    }
                }
            }
        }
        .mapStyle(viewModel.mapStyle)
        .mapControls {
            MapUserLocationButton()
            MapCompass()
            MapScaleView()
        }
        .ignoresSafeArea(.all)
    }
    
    // 顶部导航栏
    private var topButtonSection: some View {
        VStack {
            HStack {
                // 关闭按钮
                Button(action: {
                    dismiss()
                }) {
                    Image("map-return")
                        .renderingMode(.template)
                        .font(.title2)
                        .padding(12)
                }
                .padding(.leading, Theme.Spacing.md)
                
                Spacer()
                
                // 功能按钮组
                HStack(spacing: Theme.Spacing.sm) {
                    // 好友位置刷新按钮
                    Button(action: {
                        Task {
                            await friendMapViewModel.refreshFriendLocations(for: appSettings.userId)
                        }
                    }) {
                        Image(systemName: friendMapViewModel.isLoadingFriendLocations ? "arrow.clockwise" : "person.2.fill")
                            .font(.title2)
                            .foregroundColor(.white)
                            .padding(12)
                            .background(
                                Circle()
                                    .fill(.ultraThinMaterial)
                            )
                            .opacity( friendMapViewModel.isLoadingFriendLocations ? 0.5 : 1)
                    }
                    .disabled(friendMapViewModel.isLoadingFriendLocations)
                    
                    // 地图样式切换按钮
                    Button(action: {
                        viewModel.toggleMapStyle()
                    }) {
                        Image(systemName: "map")
                            .font(.title2)
                            .foregroundColor(.white)
                            .padding(12)
                            .background(
                                Circle()
                                    .fill(.ultraThinMaterial)
                            )
                    }
                }
                .padding(.trailing, Theme.Spacing.md)
            }
            .padding(.top, Theme.Spacing.md)
            
            Spacer()
        }
    }
}

// MARK: - 底部控件视图
private struct BottomControlsView: View {
    let viewModel: MapViewModel
    let friendMapViewModel: FriendMapViewModel
    @Binding var showCheckinSheet: Bool
    @Binding var showFootprintSheet: Bool

    var body: some View {
        VStack {
            
            Spacer()

            HStack {
                CheckinControlButtonsView(
                    showCheckinSheet: $showCheckinSheet,
                    showFootprintSheet: $showFootprintSheet
                )
                    .padding(.horizontal, Theme.Spacing.lg)

                // 好友位置状态显示
                if friendMapViewModel.hasFriendLocations {
                    FriendLocationStatsView(friendMapViewModel: friendMapViewModel)
                        .padding(.leading, Theme.Spacing.md)
                }
                
                Spacer()

                MapControlButtonsView(viewModel: viewModel)
                    .padding(.trailing, Theme.Spacing.md)
            }
            .padding(.bottom, Theme.Spacing.xl)
        }
    }
}

// MARK: - 好友位置统计视图
private struct FriendLocationStatsView: View {
    let friendMapViewModel: FriendMapViewModel

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            let stats = friendMapViewModel.friendLocationStats
            HStack(spacing: 8) {
                Image(systemName: "person.2.fill")
                    .foregroundColor(.brandGreen)
                    .font(.caption)

                Circle()
                    .fill(Color.green)
                    .frame(width: 6, height: 6)

                Text("\(stats.online)")
                    .font(.caption)
                    .foregroundColor(.green)

                Circle()
                    .fill(Color.gray)
                    .frame(width: 6, height: 6)

                Text("\(stats.offline)")
                    .font(.caption)
                    .foregroundColor(.gray)
            }

            if let updateTime = friendMapViewModel.formattedLastUpdateTime {
                Text(updateTime)
                    .font(.caption2)
                    .foregroundColor(.gray)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
}

// MARK: 打卡识别入口按钮
private struct CheckinControlButtonsView:View {
    @Binding var showCheckinSheet: Bool
    @Binding var showFootprintSheet: Bool

    var body: some View {

        HStack(spacing: 12) {
            Button(action: {
               print("pressed")
               }) {
               Image("icon-photo") // 来自 Assets 中的 PDF
                   .resizable()
                   .aspectRatio(contentMode: .fit)
                   .frame(
                        width: Theme.mapviewIconSize.lg,
                        height: Theme.mapviewIconSize.lg
                   )
               }

            Button(action:{
                showCheckinSheet = true
            }){
                Image("icon-checkin")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(
                         width: Theme.mapviewIconSize.lg,
                         height: Theme.mapviewIconSize.lg
                    )
            }

            // 出行打卡按钮
            Button(action:{
                showFootprintSheet = true
            }){
                Image("icon-track")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(
                         width: Theme.mapviewIconSize.lg,
                         height: Theme.mapviewIconSize.lg
                    )
            }
        }
    }
}

// MARK: - 地图控制按钮视图
private struct MapControlButtonsView: View {
    let viewModel: MapViewModel

    var body: some View {
        VStack(spacing: Theme.Spacing.md) {
            // 定位按钮
            Button(action: {
                viewModel.moveToUserLocation()
            }) {
                Image(systemName: "location.fill")
                    .font(.title2)
                    .foregroundColor(.white)
                    .padding(12)
                    .background(
                        Circle()
                            .fill(Color.brandGreen)
                            .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
                    )
            }
        }
    }
}


#Preview {
    @Previewable @State var appsettings = AppSettings()
    MapView()
        .stableBackground()
        .environmentObject(appsettings)
}
