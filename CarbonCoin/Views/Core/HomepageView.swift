//
//  HomepageView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/12.
//

import SwiftUI

/// 个人主页视图 - 显示指定用户的基本信息和公开日志
struct HomepageView: View {

    // MARK: - Properties

    /// 要显示的用户ID
    let userId: String

    /// 日志视图模型
    @StateObject private var logViewModel = LogViewModel()

    /// 用户信息
    @State private var userInfo: UserInfo?

    /// 是否正在加载用户信息
    @State private var isLoadingUserInfo = false

    // MARK: - Body

    var body: some View {
        NavigationStack {
            ZStack {
                CustomAngularGradient()

                if isLoadingUserInfo {
                    // 加载状态
                    loadingView
                } else if let error = logViewModel.errorMessage {
                    // 错误状态
                    errorView(error: error)
                } else {
                    // 主内容
                    mainContentView
                }
            }
            .navigationTitle("\(userInfo?.nickname ?? "好友" )的个人主页")
            .navigationBarTitleDisplayMode(.inline)
            .toolbarBackground(.clear, for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
        }
        .task {
            await loadUserData()
        }
        .onAppear {
            print("Enter into homepageView!!!")
        }
    }

    // MARK: - Main Content View

    private var mainContentView: some View {
        ScrollView {
            LazyVStack(spacing: Theme.Spacing.lg) {
                // 用户信息区域
                userInfoSection

                // 用户日志区域
                userLogsSection

                Spacer(minLength: 100) // 为底部TabBar留出空间
            }
            .padding(.horizontal, Theme.Spacing.md)
        }
        .scrollContentBackground(.hidden)
    }

    // MARK: - User Info Section

    private var userInfoSection: some View {
        VStack(spacing: Theme.Spacing.md) {
            // 用户头像
            AsyncImage(url: URL(string: userInfo?.avatarURL ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                Circle()
                    .fill(Color.gray.opacity(0.3))
                    .overlay(
                        Text(String(userInfo?.nickname.prefix(1) ?? "?"))
                            .font(.title1Brand)
                            .foregroundColor(.textSecondary)
                    )
            }
            .frame(width: 100, height: 100)
            .clipShape(Circle())

            // 用户昵称
            Text(userInfo?.nickname ?? "未知用户")
                .font(.title2Brand)
                .foregroundColor(.textPrimary)
                .gradientText()
        }
        .padding(Theme.Spacing.lg)
    }

    // MARK: - User Logs Section

    private var userLogsSection: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            // 标题
            HStack {
                Text("公开日志")
                    .font(.title3Brand)
                    .foregroundColor(.textPrimary)

                Spacer()

                Text("\(logViewModel.userLogs.count) 条记录")
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)
            }
            .padding(.horizontal, Theme.Spacing.md)

            // 日志列表
            if logViewModel.isLoading {
                // 加载状态
                VStack(spacing: Theme.Spacing.md) {
                    ForEach(0..<3, id: \.self) { _ in
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.gray.opacity(0.2))
                            .frame(height: 120)
                    }
                }
                .padding(.horizontal, Theme.Spacing.md)
            } else if logViewModel.userLogs.isEmpty {
                // 空状态
                emptyLogsView
            } else {
                // 日志列表
                LazyVStack(spacing: Theme.Spacing.md) {
                    ForEach(logViewModel.userLogs) { log in
                        RecordPublicItem(
                            log: log,
                            logViewModel: logViewModel,
                            currentUserId: userId,
                            displayMode: .reader // 个人主页使用reader模式
                        )
                    }
                }
                .padding(.horizontal, Theme.Spacing.md)
            }
        }
    }

    // MARK: - Loading View

    private var loadingView: some View {
        VStack(spacing: Theme.Spacing.lg) {
            ProgressView()
                .scaleEffect(1.5)
                .tint(.auxiliaryYellow)

            Text("加载中...")
                .font(.bodyBrand)
                .foregroundColor(.textSecondary)
        }
    }

    // MARK: - Error View

    private func errorView(error: String) -> some View {
        VStack(spacing: Theme.Spacing.lg) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 50))
                .foregroundColor(.red)

            Text("加载失败")
                .font(.title2Brand)
                .foregroundColor(.textPrimary)

            Text(error)
                .font(.bodyBrand)
                .foregroundColor(.textSecondary)
                .multilineTextAlignment(.center)

            Button("重试") {
                Task {
                    await loadUserData()
                }
            }
            .buttonStyle(.plain)
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
        .padding(.horizontal, Theme.Spacing.md)
    }

    // MARK: - Empty Logs View

    private var emptyLogsView: some View {
        VStack(spacing: Theme.Spacing.md) {
            Image(systemName: "doc.text")
                .font(.system(size: 40))
                .foregroundColor(.textTertiary)

            Text("暂无公开日志")
                .font(.bodyBrand)
                .foregroundColor(.textSecondary)
        }
        .padding(Theme.Spacing.xl)
        .glassCard()
        .padding(.horizontal, Theme.Spacing.md)
    }

    // MARK: - Data Loading

    /// 加载用户数据
    private func loadUserData() async {
        // 加载用户日志（只获取公开日志）
        await logViewModel.fetchUserLogs(
            userId: userId,
            isPublic: true
        )
        
        if let error = logViewModel.errorMessage {
            // 出错就直接返回，不设置 userInfo
            return
        }
        

        // 从日志中获取用户信息（如果有日志的话）
        if let firstLog = logViewModel.userLogs.first {
            userInfo = firstLog.user
        } else {
            // 如果没有日志，创建一个基本的用户信息
            userInfo = UserInfo(userId: userId, nickname: "用户\(userId.suffix(4))", avatarURL: nil)
        }
    }
}

// MARK: - Preview

#Preview {
    HomepageView(userId: "test-user-id")
        .stableBackground()
}
