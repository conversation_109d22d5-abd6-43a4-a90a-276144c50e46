//
//  RecordPublicItem.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/6.
//

import SwiftUI

// MARK: - 显示模式枚举

/// 记录显示模式枚举
enum DisplayMode {
    case author    // 作者模式 - 显示编辑按钮，用于主页
    case reader    // 阅读模式 - 隐藏编辑按钮，用于个人主页
    case `public`  // 公开模式 - 显示好友昵称，用于朋友圈
}

// MARK: - 记录详情视图

/// 记录详情视图，用于显示公开日志的详细信息，包含图标、内容、图片、点赞和评论功能
struct RecordPublicItem: View {

    // MARK: - Properties

    /// 日志数据
    let log: UserLog

    /// 日志视图模型
    @ObservedObject var logViewModel: LogViewModel

    /// 当前用户ID
    let currentUserId: String

    /// 显示模式
    let displayMode: DisplayMode

    /// 设置菜单显示状态
    @State private var showSettingsMenu = false

    /// 评论输入状态
    @State private var showCommentInput = false
    @State private var commentText = ""
    @State private var replyToComment: LogComment?

    /// 编辑页面导航状态
    @State private var showEditView = false

    // MARK: - Initialization

    init(log: UserLog, logViewModel: LogViewModel, currentUserId: String, displayMode: DisplayMode = .author) {
        self.log = log
        self.logViewModel = logViewModel
        self.currentUserId = currentUserId
        self.displayMode = displayMode
    }

    // MARK: - Body

    var body: some View {
        VStack(spacing: 0) {
            // 主内容区域
            mainContentView

            // 点赞者头像列表
            if let likes = log.likes, !likes.isEmpty {
                likedUsersView(likes: likes)
                    .padding(.trailing, Theme.Spacing.md)
                    .padding(.top, Theme.Spacing.md)
            }


            // 评论区
            commentsSection
                .padding(.leading, Theme.Spacing.xl)
        }
        .sheet(isPresented: $showEditView) {
            if displayMode == .author && log.userId == currentUserId {
                RecordEditView(log: log, logViewModel: logViewModel)
            }
        }
        .alert("设置", isPresented: $showSettingsMenu) {
            settingsMenuButtons
        }
    }

    // MARK: - Main Content View

    private var mainContentView: some View {
        HStack(spacing: Theme.Spacing.sm) {
            // 左侧图标
            iconView
                .padding(.trailing, Theme.Spacing.md)

            // 右侧内容框
            contentCardView
                .frame(maxWidth: .infinity)
        }
        
    }

    // MARK: - Icon View

    private var iconView: some View {
        ZStack {
            if displayMode == .public {
                // public模式显示用户头像，使用NavigationLink直接导航
                if let userId = log.user?.userId {
                    NavigationLink(destination: HomepageView(userId: userId)) {
                        AsyncImage(url: URL(string: log.user?.avatarURL ?? "")) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                        } placeholder: {
                            Circle()
                                .fill(Color.gray.opacity(0.3))
                                .overlay(
                                    Text(String(log.user?.nickname.prefix(1) ?? "?"))
                                        .font(.captionBrand)
                                        .foregroundColor(.textSecondary)
                                )
                        }
                        .frame(width: 48, height: 48)
                        .clipShape(Circle())
                    }
                    .buttonStyle(PlainButtonStyle()) // 移除默认按钮样式
                } else {
                    // 如果没有用户ID，显示默认头像
                    Circle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: 48, height: 48)
                        .overlay(
                            Text("?")
                                .font(.captionBrand)
                                .foregroundColor(.textSecondary)
                        )
                }
            } else {
                // author和reader模式显示活动类型图标
                // 背景圆圈
                Circle()
                    .fill(iconBackgroundColor)
                    .frame(width: 48, height: 48)

                // 图标
                Image(iconName)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 24, height: 24)
            }
        }
    }

    // MARK: - Content Card View

    private var contentCardView: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
            // 顶部：描述和设置按钮
            HStack{
                // 时间
                timeView
                    .padding(.trailing, Theme.Spacing.sm)
                
                Spacer()
                
                // 设置按钮（仅在author模式且是当前用户的日志时显示）
                if displayMode == .author && log.userId == currentUserId {
                    Button(action: {
                        showSettingsMenu = true
                    }) {
                        Image(systemName: "ellipsis")
                            .font(.title3)
                            .foregroundColor(.textSecondary)
                            .frame(width: 24, height: 24)
                    }
                    .padding(.trailing, Theme.Spacing.sm)
                }
            }
            
            textContentView

            // 活动相关组件
            activityComponentView(log: log)
                
            // 地点和碳币奖励
            locationAndRewardView
                .padding(.horizontal, Theme.Spacing.sm)

            // 图片列表
            if let imageList = log.imageList, !imageList.isEmpty {
                imageGridView(images: imageList)
            }
            
            HStack {
                Spacer()
                
                // 点赞数和评论数
                statsView
                    .padding(.trailing, Theme.Spacing.sm)
            }
        }
        .padding(Theme.Spacing.md)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                .fill(Color.recortItemBG)
                .overlay(
                    RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                        .stroke(Color.recortItemBG.opacity(0.5), lineWidth: 1)
                )
        )
    }

    // MARK: - Top Content View

    private var textContentView: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
            // 描述
            if let description = log.description, !description.isEmpty {
                Text(description)
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)
                    .multilineTextAlignment(.leading)
                    .lineLimit(nil)
            }
        }
    }

    // MARK: - Location and Reward View

    private var locationAndRewardView: some View {
        HStack {
            // 地点信息
            if let locationText = locationText {
                HStack(spacing: 4) {
                    Image(systemName: "location.fill")
                        .font(.caption)
                        .foregroundColor(.textTertiary)
                    Text(locationText)
                        .font(.captionBrand)
                        .foregroundColor(.textTertiary)
                }
            }

            Spacer()

            // 碳币奖励（写死的示例）
            HStack(spacing: Theme.Spacing.xs) {
                // 金币图标
                HStack(spacing: 2) {
                    Circle()
                        .fill(Color.orange)
                        .frame(width: 16, height: 16)
                        .overlay(
                            Text("¥")
                                .font(.system(size: 10, weight: .bold))
                                .foregroundColor(.white)
                        )
                    Text("15")
                        .font(.captionBrand)
                        .foregroundColor(.textTertiary)
                }

                // 绿叶图标
                HStack(spacing: 2) {
                    Circle()
                        .fill(Color.green)
                        .frame(width: 16, height: 16)
                        .overlay(
                            Image(systemName: "leaf.fill")
                                .font(.system(size: 8))
                                .foregroundColor(.white)
                        )
                    Text("10")
                        .font(.captionBrand)
                        .foregroundColor(.textTertiary)
                }

                // 棕色图标
                HStack(spacing: 2) {
                    Circle()
                        .fill(Color.brown)
                        .frame(width: 16, height: 16)
                        .overlay(
                            Text("C")
                                .font(.system(size: 10, weight: .bold))
                                .foregroundColor(.white)
                        )
                    Text("0.8")
                        .font(.captionBrand)
                        .foregroundColor(.textTertiary)
                }
            }
        }
    }

    // MARK: - Time View

    private var timeView: some View {
        HStack(spacing: Theme.Spacing.xs) {
            // 在public模式下显示用户昵称
            if displayMode == .public, let user = log.user {
                Text(user.nickname)
                    .font(.captionBrand)
                    .foregroundColor(.accent)
                    .fontWeight(.medium)

                Text("·")
                    .font(.captionBrand)
                    .foregroundColor(.textTertiary)
            }

            // 时间
            Text(timeText)
                .font(.captionBrand)
                .foregroundColor(.textTertiary)
        }
    }

    // MARK: - Image Grid View

    private func imageGridView(images: [String]) -> some View {
        let imageSize: CGFloat = 130
        
        let columns = Array(repeating: GridItem(.flexible(), spacing: 2), count: 2)

        return LazyVGrid(columns: columns, spacing: 8) {
            ForEach(images.prefix(6), id: \.self) { imageURL in
                AsyncImage(url: URL(string: imageURL)) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .clipped()
                        .frame(width: imageSize, height: imageSize)
                        .cornerRadius(Theme.CornerRadius.sm)
                } placeholder: {
                    RoundedRectangle(cornerRadius: Theme.CornerRadius.sm)
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: imageSize, height: imageSize)
                        .overlay(
                            ProgressView()
                                .scaleEffect(0.8)
                        )
                }
            }
        }
        .padding(.vertical, Theme.Spacing.xs)
    }

    // MARK: - Stats View

    private var statsView: some View {
        HStack {
            Spacer()

            // 点赞按钮和数量
            Button(action: {
                Task {
                    if isLikedByCurrentUser {
                        await logViewModel.unlikeLog(logId: log.id, userId: currentUserId)
                    } else {
                        await logViewModel.likeLog(logId: log.id, userId: currentUserId)
                    }
                }
            }) {
                HStack(spacing: 4) {
                    Image(systemName: isLikedByCurrentUser ? "heart.fill" : "heart")
                        .font(.system(size: 16))
                        .foregroundColor(isLikedByCurrentUser ? .red : .textSecondary)
                    Text("\(likeCount)")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)
                }
            }
            .buttonStyle(PlainButtonStyle())

            // 评论按钮和数量
            Button(action: {
                showCommentInput = true
            }) {
                HStack(spacing: 4) {
                    Image(systemName: "bubble")
                        .font(.system(size: 16))
                        .foregroundColor(.textSecondary)
                    Text("\(commentCount)")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)
                }
            }
            .buttonStyle(PlainButtonStyle())
        }
    }

    // MARK: - Liked Users View

    private func likedUsersView(likes: [LogLike]) -> some View {
        // 定义 10 列的网格布局
        let columns = Array(repeating: GridItem(.flexible(), spacing: Theme.Spacing.sm), count: 10)

        return ScrollView(.vertical, showsIndicators: false) {
            LazyVGrid(columns: columns, spacing: Theme.Spacing.sm) {
                ForEach(likes) { like in
                    AsyncImage(url: URL(string: like.user.avatarURL ?? "")) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 32, height: 32)
                            .clipShape(Circle())
                    } placeholder: {
                        Circle()
                            .fill(Color.gray.opacity(0.3))
                            .frame(width: 32, height: 32)
                            .overlay(
                                Text(String(like.user.nickname.prefix(1)))
                                    .font(.captionBrand)
                                    .foregroundColor(.textSecondary)
                            )
                    }
                }
            }
            .environment(\.layoutDirection, .rightToLeft) // 从右到左排列
            .padding(.horizontal, Theme.Spacing.md)
        }
    }

    // MARK: - Comments Section

    private var commentsSection: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
            if let comments = log.comments, !comments.isEmpty {
                ForEach(comments) { comment in
                    commentItemView(comment: comment)
                }
            }

            // 评论输入框
            if showCommentInput {
                commentInputView
            }
        }
        .padding(.horizontal, Theme.Spacing.lg)
        .padding(.top, Theme.Spacing.md)
    }

    // MARK: - Comment Item View

    private func commentItemView(comment: LogComment) -> some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
            HStack(alignment: .top, spacing: Theme.Spacing.sm) {
                // 用户头像
                AsyncImage(url: URL(string: comment.user.avatarURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 24, height: 24)
                        .clipShape(Circle())
                } placeholder: {
                    Circle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: 24, height: 24)
                        .overlay(
                            Text(String(comment.user.nickname.prefix(1)))
                                .font(.system(size: 10))
                                .foregroundColor(.textSecondary)
                        )
                }

                VStack(alignment: .leading, spacing: 4) {
                    // 用户名和时间
                    HStack {
                        Text(comment.user.nickname)
                            .font(.captionBrand)
                            .foregroundColor(.textSecondary)

                        Spacer()

                        Text(formatCommentTime(comment.createdAt))
                            .font(.system(size: 10))
                            .foregroundColor(.textTertiary)
                    }

                    // 评论内容
                    Text(comment.content)
                        .font(.bodyBrand)
                        .foregroundColor(.textPrimary)
                        .multilineTextAlignment(.leading)

                    
                    // 回复按钮
                    HStack{
                        Spacer()
                        
                        Button("回复") {
                            replyToComment = comment
                            commentText = "@\(comment.user.nickname) "
                            showCommentInput = true
                        }
                        .font(.system(size: 12))
                        .foregroundColor(.accent.opacity(0.8))
                    }
                }

                Spacer()
            }
        }
        .padding(.vertical, Theme.Spacing.xs)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.sm)
                .fill(Color.cardBackground.opacity(0.5))
        )
        .padding(.horizontal, Theme.Spacing.xs)
    }

    // MARK: - Comment Input View

    private var commentInputView: some View {
        HStack(spacing: Theme.Spacing.sm) {
            TextField("添加评论...", text: $commentText, axis: .vertical)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .lineLimit(1...3)

            Button("发送") {
                Task {
                    await submitComment()
                }
            }
            .buttonStyle(PrimaryButtonStyle())
            .disabled(commentText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)

            Button("取消") {
                showCommentInput = false
                commentText = ""
                replyToComment = nil
            }
            .buttonStyle(SecondaryButtonStyle())
        }
        .padding(.vertical, Theme.Spacing.sm)
    }

    // MARK: - Settings Menu Buttons

    private var settingsMenuButtons: some View {
        Group {
            Button(log.isPublic ? "设为私有" : "设为公开") {
                Task {
                    await logViewModel.updateUserLog(
                        logId: log.id,
                        isPublic: !log.isPublic
                    )
                }
            }

            Button("编辑") {
                showEditView = true
            }

            Button("删除", role: .destructive) {
                Task {
                    await logViewModel.deleteUserLog(logId: log.id, userId: currentUserId)
                }
            }

            Button("取消", role: .cancel) {}
        }
    }

    // MARK: - Helper Methods

    /// 提交评论
    private func submitComment() async {
        let content = commentText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !content.isEmpty else { return }

        await logViewModel.addComment(
            logId: log.id,
            userId: currentUserId,
            content: content,
            replyTo: replyToComment?.id
        )

        // 清空输入
        commentText = ""
        replyToComment = nil
        showCommentInput = false
    }

    /// 格式化评论时间
    private func formatCommentTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")

        let now = Date()
        let timeInterval = now.timeIntervalSince(date)

        if timeInterval < 60 {
            return "刚刚"
        } else if timeInterval < 3600 {
            return "\(Int(timeInterval / 60))分钟前"
        } else if timeInterval < 86400 {
            return "\(Int(timeInterval / 3600))小时前"
        } else {
            formatter.dateFormat = "MM-dd HH:mm"
            return formatter.string(from: date)
        }
    }

    // MARK: - Computed Properties

    /// 图标名称
    private var iconName: String {
        // 根据记录类型选择图标，总是根据类型显示
        switch log.recordType {
        case .location:
            return "log-checkin"
        case .recognition:
            return "log-shopping"
        case .trip:
            // 足迹记录需要根据活动类型选择图标
            if let footprint = log.userFootprints {
                switch footprint.activityType.rawValue {
                case "walking":
                    return "log-walk"
                case "cycling":
                    return "log-cycling"
                case "bus":
                    return "log-bus"
                case "subway":
                    return "log-subway"
                default:
                    return "log-walk" // 默认使用步行图标
                }
            }
            return "log-walk"
        }
    }

    /// 图标背景颜色
    private var iconBackgroundColor: Color {
        return Color.accent
    }

    /// 位置文本
    private var locationText: String? {
        switch log.recordType {
        case .location:
            if let checkin = log.locationCheckIns {
                return checkin.position
            }
            return nil
        case .recognition:
            if let userItemCard = log.cardAcquisitionRecord {
                return userItemCard.card?.location
            }
            return nil
        case .trip:
            return "足迹记录" // 足迹记录显示固定文本
        }
    }

    /// 时间文本
    private var timeText: String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.dateFormat = "M/d/yyyy h:mm a"
        return formatter.string(from: log.createdAt)
    }

    /// 当前用户是否已点赞
    private var isLikedByCurrentUser: Bool {
        return logViewModel.isLogLikedByUser(logId: log.id, userId: currentUserId)
    }

    /// 点赞数
    private var likeCount: Int {
        return logViewModel.getLikeCount(for: log.id)
    }

    /// 评论数
    private var commentCount: Int {
        return logViewModel.getCommentCount(for: log.id)
    }
}

// MARK: - Preview

#Preview {
    ScrollView {
        VStack(spacing: Theme.Spacing.lg) {
            // 地点打卡示例
            RecordPublicItem(
                log: UserLog(
                    id: "1",
                    userId: "user1",
                    recordType: .location,
                    recordId: "checkin1",
                    imageList: [
                        "https://example.com/image1.jpg",
                        "https://example.com/image2.jpg",
                        "https://example.com/image3.jpg"
                    ],
                    description: "在路上看到了一只超级可爱的小狗",
                    isPublic: true,
                    createdAt: Date(),
                    updatedAt: Date(),
                    user: UserInfo(userId: "user1", nickname: "测试用户", avatarURL: nil),
                    likes: Array(repeating: LogLike(id: "like1", userId: "user2", createdAt: Date(), user: UserInfo(userId: "user2", nickname: "用户2", avatarURL: nil)), count: 15),
                    comments: [
                        LogComment(id: "comment1", userId: "user3", content: "看到可爱小狗我会开心一整天😊", replyTo: nil, createdAt: Date(), user: UserInfo(userId: "user3", nickname: "lalalano", avatarURL: nil)),
                        LogComment(id: "comment2", userId: "user4", content: "我刚刚路过公园也看到了，真的好可爱，还超级亲人，小狗主人让我摸来着", replyTo: nil, createdAt: Date(), user: UserInfo(userId: "user4", nickname: "lalalano", avatarURL: nil)),
                        LogComment(id: "comment3", userId: "user5", content: "抱走😍", replyTo: nil, createdAt: Date(), user: UserInfo(userId: "user5", nickname: "lalalano", avatarURL: nil))
                    ],
                    locationCheckIns: LocationCheckIn(id: "checkin1", position: "杭州市 余杭区城市街道", latitude: 30.0, longitude: 120.0, createdAt: Date()),
                    userFootprints: nil,
                    cardAcquisitionRecord: nil
                ),
                logViewModel: LogViewModel(),
                currentUserId: "user1",
                displayMode: .public
            )
        }
        .padding()
    }
    .globalBackground()
}
