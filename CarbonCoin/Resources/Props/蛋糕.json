{"v": "5.9.6", "ip": 0, "op": 188, "fr": 29, "w": 1080, "h": 1080, "nm": "C", "assets": [], "layers": [{"ind": 1, "nm": "N", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [540, 540, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [800, 800, 100], "l": 2}}, "shapes": [], "ip": 0, "op": 600, "st": 0, "ty": 4}, {"ind": 2, "nm": "1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -21, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -18.063, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 5.438, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 8.375, "s": [0]}, {"t": 26, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-48.575, 11.411, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [105.263, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6, 1], [-6, -1]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -21, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -11.208, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 8.375, "s": [100]}, {"t": 26, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -21, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -1.418, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 8.375, "s": [100]}, {"t": 26, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 16, "ty": 4}, {"ind": 3, "nm": "2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -18, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -15.063, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 8.438, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 11.375, "s": [0]}, {"t": 29, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-40.801, 4.256, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [105.263, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[2.615, 4.155], [-2.615, -4.155]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -18, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -8.208, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 11.375, "s": [100]}, {"t": 29, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -18, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 1.583, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 11.375, "s": [100]}, {"t": 29, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 16, "ty": 4}, {"ind": 4, "nm": "3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -15, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -12.063, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 11.438, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 14.375, "s": [0]}, {"t": 32, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-51.101, -11.284, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [105.263, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[4.6, 7.305], [-4.6, -7.305]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -15, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -5.208, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 14.375, "s": [100]}, {"t": 32, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -15, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 4.583, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 14.375, "s": [100]}, {"t": 32, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 16, "ty": 4}, {"ind": 5, "nm": "4", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -12, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -9.063, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 14.438, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 17.375, "s": [0]}, {"t": 35, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-33.838, -5.089, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [105.263, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[1, 8.5], [-1, -8.5]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -12, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -2.208, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 17.375, "s": [100]}, {"t": 35, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -12, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 7.583, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 17.375, "s": [100]}, {"t": 35, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 16, "ty": 4}, {"ind": 6, "nm": "5", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -9, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -6.063, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 17.438, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 20.375, "s": [0]}, {"t": 38, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [32.478, -4.089, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [105.263, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-1, 8.5], [1, -8.5]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -9, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0.793, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 20.375, "s": [100]}, {"t": 38, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -9, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10.583, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 20.375, "s": [100]}, {"t": 38, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 16, "ty": 4}, {"ind": 7, "nm": "6", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -6, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -3.063, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 20.438, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 23.375, "s": [0]}, {"t": 41, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [39.283, 5.496, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [105.263, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-2.465, 3.915], [2.465, -3.915]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -6, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 3.793, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 23.375, "s": [100]}, {"t": 41, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -6, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 13.583, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 23.375, "s": [100]}, {"t": 41, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 16, "ty": 4}, {"ind": 8, "nm": "7", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -3, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -0.063, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 23.438, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26.375, "s": [0]}, {"t": 44, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [49.878, -10.489, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [92.391, 105.304, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-4.47, 7.1], [4.47, -7.1]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -3, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 6.793, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26.375, "s": [100]}, {"t": 44, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -3, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16.583, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26.375, "s": [100]}, {"t": 44, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 16, "ty": 4}, {"ind": 9, "nm": "8", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 2.938, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26.438, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 29.375, "s": [0]}, {"t": 47, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [47.215, 12.411, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [105.263, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-6, 1], [6, -1]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 9.793, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 29.375, "s": [100]}, {"t": 47, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 19.583, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 29.375, "s": [100]}, {"t": 47, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 16, "ty": 4}, {"ind": 10, "nm": "2", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-16.763, -51.621, 0], "l": 2}, "a": {"a": 0, "k": [-11.763, -49.121, 0], "l": 2}, "s": {"k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [30, 30, 100]}, {"t": 31, "s": [110, 110, 100]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');", "a": 1, "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [1.934, 0], [-1.934, 0]], "o": [[1.93, 0], [-1.93, 0], [0, 0]], "v": [[-11.763, -47.621], [-11.763, -50.621], [-11.763, -47.621]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 1, "ty": 4}, {"ind": 11, "nm": "2", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -5, "s": [90]}, {"t": 42, "s": [-90]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');"}, "p": {"a": 0, "k": [268, 250.354, 0], "l": 2}, "a": {"a": 0, "k": [-34, -36.206, 0], "l": 2}, "s": {"a": 0, "k": [800, 800, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-29.741, -37.658], [-38.259, -34.753]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-35.453, -40.465], [-32.547, -31.947]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "ty": 4}, {"ind": 12, "nm": "2", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -15, "s": [-90]}, {"t": 32, "s": [90]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');"}, "p": {"a": 0, "k": [765.571, 157.244, 0], "l": 2}, "a": {"a": 0, "k": [20.696, -47.844, 0], "l": 2}, "s": {"a": 0, "k": [800, 800, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0.482, 1.536], [-2.901, 0.909], [0.589, -1.463]], "o": [[0.751, -1.283], [0.909, 2.901], [-1.632, 0.511], [0, 0]], "v": [[18.555, -48.693], [19.051, -53.093], [25.945, -49.489], [22.503, -46.325]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.627, 0.305, 0.015, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0.165, 0.527], [-0.527, 0.165], [0.742, 2.368], [-0.527, 0.165], [-0.165, -0.527], [-2.368, 0.742], [-0.165, -0.527], [0.527, -0.165], [-0.742, -2.368], [0.527, -0.165], [0.165, 0.527], [2.368, -0.742]], "o": [[-0.165, -0.527], [2.368, -0.742], [-0.165, -0.527], [0.527, -0.165], [0.742, 2.368], [0.527, -0.165], [0.165, 0.527], [-2.368, 0.742], [0.165, 0.527], [-0.527, 0.165], [-0.742, -2.368], [-0.527, 0.165]], "v": [[14.494, -45.9], [15.149, -47.154], [18.097, -52.794], [18.752, -54.047], [20.006, -53.392], [25.646, -50.444], [26.899, -49.788], [26.244, -48.535], [23.296, -42.895], [22.64, -41.642], [21.387, -42.297], [15.747, -45.245]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [1, 0.647, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "ty": 4}, {"ind": 13, "nm": "2", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [90]}, {"t": 47, "s": [-90]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');"}, "p": {"a": 0, "k": [860, 304, 0], "l": 2}, "a": {"a": 0, "k": [32, -29, 0], "l": 2}, "s": {"a": 0, "k": [800, 800, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0.5], [1.93, 0], [0, -1.93], [-1.04, -0.6]], "o": [[0.2, -0.43], [0, -1.93], [-1.93, 0], [0, 1.29], [0, 0]], "v": [[35.69, -28.08], [36, -29.5], [32.5, -33], [29, -29.5], [30.74, -26.48]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.003, 0.352, 0.184, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, -1.933], [1.933, 0], [0, 1.933], [-1.933, 0]], "o": [[0, 1.933], [-1.933, 0], [0, -1.933], [1.933, 0]], "v": [[36, -29.5], [32.5, -26], [29, -29.5], [32.5, -33]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.529, 0.874, 0.458, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "ty": 4}, {"ind": 14, "nm": "W", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [540, 944, 0], "l": 2}, "a": {"a": 0, "k": [0, 51, 0], "l": 2}, "s": {"a": 0, "k": [800, 800, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0.14, 0]], "o": [[-3.65, 0], [0, 0]], "v": [[-44, 51], [-50, 51]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [6.17, 0], [0, 0], [0, 0], [0, 0], [0.92, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-1.02, 0], [0, 0]], "v": [[50, 51], [40, 51], [29, 51], [-27, 51], [-38, 51], [-40.91, 51]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "ty": 4}, {"ind": 15, "nm": "C", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 1.001, 0], "l": 2}, "a": {"a": 0, "k": [0, 1.001, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [1.934, 0], [-1.934, 0]], "o": [[1.93, 0], [-1.93, 0], [0, 0]], "v": [[-9.017, -5.176], [-9.017, -8.176], [-9.017, -5.176]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, -0.828], [0.828, 0], [0, 0.828], [-0.828, 0]], "o": [[0, 0.828], [-0.828, 0], [0, -0.828], [0.828, 0]], "v": [[7, -5.5], [5.5, -4], [4, -5.5], [5.5, -7]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [1, 0.8, 0.329, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, -2.38], [0, 0], [0, 6.67], [0, 0], [0, -2.67], [0, 0], [0, 4], [0, 0], [0, -4], [0, 0], [0, 4], [0, 0], [0, -4], [0, 0], [0, 4], [0, 0], [6.87, 0], [0, 0], [2.05, -1.49]], "o": [[-1.16, 1.89], [0, 0], [0, 6.67], [0, 0], [0, -2.67], [0, 0], [0, 4], [0, 0], [0, -4], [0, 0], [0, 4], [0, 0], [0, -4], [0, 0], [0, 4], [0, 0], [0, -6.87], [0, 0], [-2.73, 0], [0, 0]], "v": [[-21.17, -5.06], [-23, 1.43], [-23, 8], [-13, 8], [-13, 0], [-9, 0], [-9, 7], [-3, 7], [-3, 0], [2, 0], [2, 7], [9, 7], [9, -1], [15, -1], [15, 8], [24, 8], [24, 1.43], [11.57, -11], [-10.57, -11], [-17.88, -8.63]], "c": false}, "a": 0}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, -6.87], [0, 0], [2.61, 0.56], [0, 0], [6.87, 0], [0, 0]], "o": [[0, 0], [0, 2.45], [0, 0], [0, -6.87], [0, 0], [6.87, 0]], "v": [[24, 1.43], [24, 8], [18, 10.84], [18, 1.43], [5.57, -11], [11.57, -11]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.07, 0.592, 0.8, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, -6.87], [0, 0], [0, 4], [0, 0], [0, -4], [0, 0], [0, 4], [0, 0], [0, -4], [0, 0], [0, 4], [0, 0], [0, -2.67], [0, 0], [2.13, -0.38], [0, 3.78], [0, 0], [-6.87, 0], [0, 0]], "o": [[0, 0], [0, 4], [0, 0], [0, -4], [0, 0], [0, 4], [0, 0], [0, -4], [0, 0], [0, 4], [0, 0], [0, -2.67], [0, 0], [0, 2.89], [-2.78, 0.52], [0, 0], [0, -6.87], [0, 0], [6.87, 0]], "v": [[24, 1.43], [24, 8], [15, 8], [15, -1], [9, -1], [9, 7], [2, 7], [2, 0], [-3, 0], [-3, 7], [-9, 7], [-9, 0], [-13, 0], [-13, 8], [-17, 12.9], [-23, 8], [-23, 1.43], [-10.57, -11], [11.57, -11]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0, 0.85, 0.976, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 16, "ty": 4}, {"ind": 16, "nm": "C", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 3, 0], "l": 2}, "a": {"a": 0, "k": [0, 3, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [-2.73, 0], [0, 0], [0, -6.87], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.16, 1.89]], "o": [[2.05, -1.49], [0, 0], [6.87, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.38], [0, 0]], "v": [[-17.88, -8.63], [-10.57, -11], [11.57, -11], [24, 1.43], [24, 8], [24, 17], [-23, 17], [-23, 8], [-23, 1.43], [-21.17, -5.06]], "c": false}, "a": 0}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, -6.87], [0, 0], [0, 0], [0, 0], [6.87, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, -6.87], [0, 0], [6.87, 0]], "v": [[24, 1.43], [24, 17], [18, 17], [18, 1.43], [5.57, -11], [11.57, -11]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.878, 0.592, 0.247, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[6.862, 0], [0, 0], [0, -6.862], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-6.862, 0], [0, 0], [0, 0], [0, 0], [0, -6.862]], "v": [[11.575, -11], [-10.575, -11], [-23, 1.425], [-23, 17], [24, 17], [24, 1.425]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [1, 0.8, 0.329, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 18, "ty": 4}, {"ind": 17, "nm": "C", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0.385, 28.999, 0], "l": 2}, "a": {"a": 0, "k": [0.385, 28.999, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [1.934, 0], [-1.934, 0]], "o": [[1.93, 0], [-1.93, 0], [0, 0]], "v": [[-20.561, 34.524], [-20.561, 31.524], [-20.561, 34.524]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [1.934, 0], [-1.934, 0]], "o": [[1.93, 0], [-1.93, 0], [0, 0]], "v": [[21.743, 35.369], [21.743, 32.369], [21.743, 35.369]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [1.934, 0], [-1.934, 0]], "o": [[1.93, 0], [-1.93, 0], [0, 0]], "v": [[9.988, 21.784], [9.988, 18.784], [9.988, 21.784]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, -1.105], [1.105, 0], [0, 1.105], [-1.105, 0]], "o": [[0, 1.105], [-1.105, 0], [0, -1.105], [1.105, 0]], "v": [[-20, 24], [-22, 26], [-24, 24], [-22, 22]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [1, 0.8, 0.329, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, -1.105], [1.105, 0], [0, 1.105], [-1.105, 0]], "o": [[0, 1.105], [-1.105, 0], [0, -1.105], [1.105, 0]], "v": [[25, 24], [23, 26], [21, 24], [23, 22]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [1, 0.8, 0.329, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [-0.3, 0.83], [-0.13, 0.27], [-0.65, 0.76]], "o": [[0.1, -0.91], [0.11, -0.28], [0.42, -0.93], [0, 0]], "v": [[-32.93, 27], [-32.32, 24.38], [-31.97, 23.55], [-30.35, 21]], "c": false}, "a": 0}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [-0.05, 0.02], [-0.11, 0.05], [-0.15, 0.07], [-0.01, 0], [-0.16, 0.05], [-0.2, 0.05], [-0.13, 0.03], [-0.31, 0.05], [-0.19, 0.02], [-0.39, 0], [0, 0], [-0.24, -0.01], [-0.14, -0.02], [-0.73, -0.23], [-0.3, -0.12], [-0.28, -0.15], [-0.14, -0.08], [-0.15, -0.1], [-0.21, -0.16], [-0.38, -0.39], [-0.12, -0.14], [-0.25, -0.36], [-0.07, -0.11], [-0.16, -0.3], [-0.12, -0.28], [-0.06, -0.15], [-0.05, -0.14], [-0.04, -0.16], [-0.07, -0.31], [0, -0.01], [0, 0], [0, -1.63], [0, 0], [0, 5.33], [0, 0], [0, -2.67], [0, 5.33], [0, -4], [0, 0], [0, 4], [0, 0], [0, -6.67], [0, 0], [0, 4], [0, 0], [1.88, 0], [0, 0], [0, 0]], "o": [[0.05, -0.02], [0.1, -0.05], [0.15, -0.07], [0.01, 0], [0.16, -0.06], [0.2, -0.07], [0.13, -0.04], [0.3, -0.07], [0.19, -0.03], [0.38, -0.04], [0, 0], [0.25, 0], [0.14, 0.01], [0.78, 0.08], [0.31, 0.1], [0.29, 0.13], [0.14, 0.07], [0.16, 0.09], [0.23, 0.15], [0.43, 0.32], [0.13, 0.12], [0.3, 0.32], [0.08, 0.1], [0.19, 0.28], [0.14, 0.26], [0.06, 0.14], [0.06, 0.14], [0.06, 0.16], [0.09, 0.31], [0, 0.01], [0, 0], [-1.66, 0], [0, 0], [0, 5.33], [0, 0], [0, -2.67], [0, 5.33], [0, -4], [0, 0], [0, 4], [0, 0], [0, -6.67], [0, 0], [0, 4], [0, 0], [0, -1.85], [0, 0], [0, 0], [0, 0]], "v": [[-26.78, 18.19], [-26.64, 18.12], [-26.32, 17.97], [-25.86, 17.77], [-25.84, 17.77], [-25.37, 17.6], [-24.76, 17.41], [-24.37, 17.31], [-23.46, 17.13], [-22.9, 17.06], [-21.75, 17], [22.75, 17], [23.48, 17.02], [23.9, 17.06], [26.16, 17.53], [27.07, 17.86], [27.93, 18.27], [28.35, 18.5], [28.82, 18.78], [29.48, 19.24], [30.7, 20.3], [31.07, 20.69], [31.9, 21.72], [32.12, 22.03], [32.64, 22.89], [33.04, 23.7], [33.22, 24.14], [33.38, 24.56], [33.53, 25.05], [33.77, 25.98], [33.77, 26], [29.89, 26], [26.89, 28.95], [26.89, 37], [17.76, 37], [17.76, 25], [13.7, 25], [5.58, 25], [-0.52, 25], [-0.52, 31], [-5.59, 31], [-5.59, 26], [-16.76, 26], [-16.76, 37], [-24.88, 37], [-24.88, 30.35], [-28.28, 27], [-32.93, 27], [-33, 27]], "c": false}, "a": 0}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[-0.96, -4.73], [0, 0], [0.34, -1.25], [5.59, 0.62], [0.42, 0], [0, 0], [-0.41, -0.05]], "o": [[0, 0], [-1.38, 0], [-0.04, -5.75], [-0.41, -0.05], [0, 0], [0.42, 0], [4.88, 0.54]], "v": [[33.77, 26], [29.89, 26], [27, 28.17], [17, 17.07], [15.75, 17], [22.75, 17], [24, 17.07]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.07, 0.592, 0.8, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[-0.96, -4.73], [0, 0], [0, -1.63], [0, 0], [1.61, -0.5], [0, 3.33], [0, 0], [0, -2.67], [0, 5.33], [0, -4], [0, 0], [0, 4], [0, 0], [0, -6.67], [0, 0], [0, 4], [0, 0], [0.69, 0.61], [0.89, 0], [0, 0], [0, 0.01], [-0.03, 0.16], [-0.02, 0.11], [-0.04, 0.16], [-0.38, 0.75], [-0.09, 0.15], [-0.19, 0.28], [-0.09, 0.11], [-0.9, 0.66], [-0.12, 0.08], [-0.3, 0.16], [-0.15, 0.07], [-0.84, 0.2], [-0.18, 0.03], [-0.12, 0.02], [-0.17, 0.02], [-0.01, 0], [0, 0], [-0.2, 0.01], [-0.21, 0], [0, 0], [-0.41, -0.05]], "o": [[0, 0], [-1.66, 0], [0, 0], [0, 2], [-2.67, 0.83], [0, 0], [0, -2.67], [0, 5.33], [0, -4], [0, 0], [0, 4], [0, 0], [0, -6.67], [0, 0], [0, 4], [0, 0], [0, -0.98], [-0.6, -0.54], [0, 0], [0, -0.01], [0.02, -0.17], [0.02, -0.12], [0.03, -0.18], [0.2, -0.85], [0.07, -0.15], [0.16, -0.3], [0.08, -0.12], [0.66, -0.9], [0.11, -0.09], [0.28, -0.19], [0.15, -0.09], [0.76, -0.38], [0.16, -0.04], [0.11, -0.02], [0.16, -0.03], [0.01, 0], [0, 0], [0.2, -0.03], [0.21, -0.02], [0, 0], [0.42, 0], [4.88, 0.54]], "v": [[33.77, 26], [29.89, 26], [26.89, 28.95], [26.89, 37], [24, 40.75], [17.76, 37], [17.76, 25], [13.7, 25], [5.58, 25], [-0.52, 25], [-0.52, 31], [-5.59, 31], [-5.59, 26], [-16.76, 26], [-16.76, 37], [-24.88, 37], [-24.88, 30.35], [-25.99, 27.88], [-28.28, 27], [-32.93, 27], [-32.93, 26.98], [-32.86, 26.48], [-32.8, 26.14], [-32.69, 25.63], [-31.81, 23.22], [-31.57, 22.77], [-31.04, 21.91], [-30.79, 21.56], [-28.44, 19.21], [-28.09, 18.96], [-27.23, 18.43], [-26.78, 18.19], [-24.37, 17.31], [-23.86, 17.2], [-23.52, 17.14], [-23.02, 17.07], [-23, 17.07], [-22.99, 17.07], [-22.38, 17.02], [-21.75, 17], [22.75, 17], [24, 17.07]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0, 0.85, 0.976, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 18, "ty": 4}, {"ind": 18, "nm": "C", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 30, 0], "l": 2}, "a": {"a": 0, "k": [0, 30, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [-0.04, 0.02], [0, 0], [-0.11, 0.05], [-0.16, 0.07], [-0.01, 0], [-0.16, 0.05], [-0.2, 0.05], [-0.13, 0.03], [-0.31, 0.05], [-0.19, 0.02], [-0.39, 0], [0, 0], [-0.24, -0.01], [-0.14, -0.02], [-0.73, -0.23], [-0.3, -0.12], [-0.28, -0.15], [-0.13, -0.09], [-0.15, -0.1], [-0.21, -0.16], [-0.38, -0.39], [-0.12, -0.13], [-0.25, -0.36], [-0.07, -0.11], [-0.16, -0.3], [-0.12, -0.28], [-0.05, -0.15], [-0.05, -0.14], [-0.04, -0.16], [-0.06, -0.31], [0, -0.01], [0, -0.77], [0, 0], [0, 0], [0, 0], [-0.05, 0.41], [-0.3, 0.83], [-0.13, 0.27], [-0.65, 0.76], [0, 0]], "o": [[0.04, -0.03], [0, 0], [0.11, -0.06], [0.15, -0.07], [0.01, 0], [0.15, -0.06], [0.2, -0.07], [0.13, -0.04], [0.3, -0.07], [0.18, -0.03], [0.38, -0.04], [0, 0], [0.25, 0], [0.14, 0.01], [0.78, 0.08], [0.31, 0.09], [0.29, 0.12], [0.14, 0.07], [0.16, 0.09], [0.23, 0.15], [0.43, 0.32], [0.13, 0.13], [0.3, 0.32], [0.08, 0.1], [0.19, 0.28], [0.14, 0.26], [0.07, 0.14], [0.06, 0.14], [0.06, 0.16], [0.1, 0.31], [0, 0.01], [0.15, 0.73], [0, 0], [0, 0], [0, 0], [0, -0.42], [0.1, -0.91], [0.11, -0.28], [0.42, -0.93], [0, 0], [0, 0]], "v": [[-26.78, 18.19], [-26.65, 18.12], [-26.64, 18.12], [-26.32, 17.97], [-25.86, 17.77], [-25.84, 17.77], [-25.37, 17.6], [-24.76, 17.41], [-24.37, 17.31], [-23.46, 17.13], [-22.9, 17.06], [-21.75, 17], [22.75, 17], [23.48, 17.02], [23.9, 17.06], [26.16, 17.53], [27.07, 17.86], [27.93, 18.27], [28.35, 18.5], [28.82, 18.78], [29.48, 19.24], [30.7, 20.3], [31.07, 20.69], [31.9, 21.72], [32.12, 22.03], [32.64, 22.89], [33.04, 23.7], [33.22, 24.14], [33.38, 24.56], [33.53, 25.05], [33.77, 25.98], [33.77, 26], [34, 28.25], [34, 44], [-33, 44], [-33, 28.25], [-32.93, 27], [-32.32, 24.38], [-31.97, 23.55], [-30.35, 21], [-30.34, 21]], "c": false}, "a": 0}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, -5.79], [0, 0], [0, 0], [0, 0], [5.62, 0.62], [0.42, 0], [0, 0], [-0.41, -0.05]], "o": [[0, 0], [0, 0], [0, 0], [0, -5.79], [-0.41, -0.05], [0, 0], [0.42, 0], [5.62, 0.62]], "v": [[34, 28.25], [34, 44], [27, 44], [27, 28.25], [17, 17.07], [15.75, 17], [22.75, 17], [24, 17.07]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.878, 0.592, 0.247, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, -5.79], [0, 0], [0, 0], [0, 0], [-6.21, 0], [0, 0], [-0.41, -0.05]], "o": [[0, 0], [0, 0], [0, 0], [0, -6.21], [0, 0], [0.42, 0], [5.62, 0.62]], "v": [[34, 28.25], [34, 44], [-33, 44], [-33, 28.25], [-21.75, 17], [22.75, 17], [24, 17.07]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [1, 0.8, 0.329, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 21, "ty": 4}, {"ind": 19, "nm": "C", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [548.768, 944.714, 0], "ti": [0, 5.833, 0], "to": [0, 3, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 11.016, "s": [548.768, 962.714, 0], "ti": [0, 3, 0], "to": [0, -5.833, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 25.704, "s": [548.768, 909.714, 0], "ti": [0, -5.833, 0], "to": [0, -3, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 36.719, "s": [548.768, 944.714, 0], "ti": [0, 0, 0], "to": [0, 0, 0]}, {"t": 47, "s": [548.768, 944.714, 0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();", "a": 1, "l": 2}, "a": {"a": 0, "k": [1, 51, 0], "l": 2}, "s": {"a": 0, "k": [760, 800, 100], "l": 2}}, "ip": 0, "op": 600, "st": 0, "ty": 3}, {"ind": 20, "nm": "M", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [540, 540, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "R", "bm": 0, "it": [{"ty": "rc", "nm": "R", "d": 1, "s": {"a": 0, "k": [916.02, 125.564]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 1, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 0}, "c": {"a": 0, "k": [0, 0, 0, 1]}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.917, 0.619, 0.49, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [13.627, 466.411]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "td": 1, "ty": 4}, {"ind": 21, "nm": "C", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [1, 51, 0], "l": 2}, "a": {"a": 0, "k": [1, 51, 0], "l": 2}, "s": {"k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1.075, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.037, 0]}, "t": 11.016, "s": [110.526, 90, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0.167, 0]}, "t": 22.031, "s": [100, 110, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 33.048, "s": [100, 100, 100]}, {"t": 47, "s": [100, 100, 100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();", "a": 1, "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [-3.866, 0], [0, 0], [0, -3.866]], "o": [[0, 0], [0, 0], [0, -3.866], [0, 0], [3.866, 0], [0, 0]], "v": [[40, 51], [-38, 51], [-38, 51], [-31, 44], [33, 44], [40, 51]], "c": true}, "a": 0}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, -3.87], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [3.87, 0]], "v": [[40, 51], [29, 51], [29, 44], [33, 44]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.847, 0.266, 0.364, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, -3.87], [0, 0], [-1.27, 1.27], [-1.94, 0], [0, 0]], "o": [[0, 0], [0, -1.94], [1.27, -1.27], [0, 0], [3.87, 0]], "v": [[40, 51], [-38, 51], [-35.95, 46.05], [-31, 44], [33, 44]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [1, 0.341, 0.431, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 19, "tt": 2, "ty": 4}, {"ind": 22, "nm": "F", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -10, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 8, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 18, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 27, "s": [-3]}, {"t": 37, "s": [3]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');"}, "p": {"a": 0, "k": [10.171, -26.292, 0], "l": 2}, "a": {"a": 0, "k": [10.171, -26.292, 0], "l": 2}, "s": {"k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": -10, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [110, 110, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 8, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 18, "s": [110, 110, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 27, "s": [100, 100, 100]}, {"t": 37, "s": [110, 110, 100]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');", "a": 1, "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[-1.606, 4.016], [4.819, -3.212]], "o": [[0, 0], [-3.061, -1.845]], "v": [[10.875, -35.834], [10.072, -27]], "c": true}, "a": 0}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[-1.606, 4.016], [4.819, -3.212]], "o": [[0, 0], [-3.061, -1.845]], "v": [[10.875, -35.834], [10.072, -27]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [1, 0.8, 0.329, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 23, "ty": 4}, {"ind": 23, "nm": "C", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [10.562, -9.75, 0], "ti": [0, 1.25, 0], "to": [0, 0.833, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 11, "s": [10.562, -4.75, 0], "ti": [0, 0.833, 0], "to": [0, -1.25, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [10.562, -17.25, 0], "ti": [0, -1.25, 0], "to": [0, -0.833, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 39, "s": [10.562, -9.75, 0], "ti": [0, 0, 0], "to": [0, 0, 0]}, {"t": 47, "s": [10.562, -9.75, 0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();", "a": 1, "l": 2}, "a": {"a": 0, "k": [10.562, -11, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [-0.933, 0], [0, 0], [0, -0.933]], "o": [[0, 0], [0, 0], [0, -0.933], [0, 0], [0.933, 0], [0, 0]], "v": [[13, -11], [8, -11], [8, -21.31], [9.69, -23], [11.31, -23], [13, -21.31]], "c": true}, "a": 0}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 2.76], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [2.76, 0]], "v": [[13, -21], [13, -11], [8, -11], [8, -16]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.847, 0.266, 0.364, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 50}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [-0.933, 0], [0, 0], [0, -0.933]], "o": [[0, 0], [0, 0], [0, -0.933], [0, 0], [0.933, 0], [0, 0]], "v": [[13, -11], [8, -11], [8, -21.31], [9.69, -23], [11.31, -23], [13, -21.31]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [1, 0.341, 0.431, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 19, "ty": 4}, {"ind": 24, "nm": "F", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 9, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 18, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 28, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 37, "s": [-3]}, {"t": 47, "s": [3]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');"}, "p": {"a": 0, "k": [-0.288, -31.875, 0], "l": 2}, "a": {"a": 0, "k": [-0.288, -31.875, 0], "l": 2}, "s": {"k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 9, "s": [110, 110, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 18, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 28, "s": [110, 110, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 37, "s": [100, 100, 100]}, {"t": 47, "s": [110, 110, 100]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');", "a": 1, "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [-0.43, 1.8], [5, 0], [-0.79, 1.17]], "o": [[0.89, -1.01], [0, 0], [-2.14, -2.14], [0, 0]], "v": [[0.54, -38.93], [2.73, -43], [-0.27, -32], [-1.64, -36.52]], "c": false}, "a": 0}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[-1.426, 6.062], [5, 0]], "o": [[0, 0], [-5, -5]], "v": [[2.727, -43], [-0.273, -32]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [1, 0.8, 0.329, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 25, "ty": 4}, {"ind": 25, "nm": "C", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [0.438, -9.75, 0], "ti": [0, 1.25, 0], "to": [0, 0.833, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 11, "s": [0.438, -4.75, 0], "ti": [0, 0.833, 0], "to": [0, -1.25, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 29, "s": [0.438, -17.25, 0], "ti": [0, -1.25, 0], "to": [0, -0.833, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [0.438, -9.75, 0], "ti": [0, 0, 0], "to": [0, 0, 0]}, {"t": 47, "s": [0.438, -9.75, 0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();", "a": 1, "l": 2}, "a": {"a": 0, "k": [0.438, -11, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0.93, 0], [0, 0], [0, -0.93], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, -0.93], [0, 0], [-0.93, 0], [0, 0], [0, 0]], "v": [[-3, -22.67], [-3, -11], [4, -11], [4, -27.31], [2.31, -29], [-1.31, -29], [-3, -27.31], [-3, -26]], "c": false}, "a": 0}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 3.87], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [3.87, 0]], "v": [[4, -26], [4, -11], [-3, -11], [-3, -19]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.847, 0.266, 0.364, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 50}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [-0.933, 0], [0, 0], [0, -0.933]], "o": [[0, 0], [0, 0], [0, -0.933], [0, 0], [0.933, 0], [0, 0]], "v": [[4, -11], [-3, -11], [-3, -27.31], [-1.31, -29], [2.31, -29], [4, -27.31]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [1, 0.341, 0.431, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 19, "ty": 4}, {"ind": 26, "nm": "F", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -5, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 4, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 13, "s": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 23, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 32, "s": [-3]}, {"t": 42, "s": [3]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');"}, "p": {"a": 0, "k": [-9.882, -26.167, 0], "l": 2}, "a": {"a": 0, "k": [-9.882, -26.167, 0], "l": 2}, "s": {"k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": -5, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 4, "s": [110, 110, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 13, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 23, "s": [110, 110, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 32, "s": [100, 100, 100]}, {"t": 42, "s": [110, 110, 100]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');", "a": 1, "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[-1.606, 4.016], [4.819, -3.212]], "o": [[0, 0], [-3.061, -1.845]], "v": [[-9.125, -35.834], [-9.928, -27]], "c": true}, "a": 0}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[-1.606, 4.016], [4.819, -3.212]], "o": [[0, 0], [-3.061, -1.845]], "v": [[-9.125, -35.834], [-9.928, -27]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [1, 0.8, 0.329, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 27, "ty": 4}, {"ind": 27, "nm": "C", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [-9, -9.812, 0], "ti": [0, 1.25, 0], "to": [0, 0.833, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 11, "s": [-9, -4.812, 0], "ti": [0, 0.833, 0], "to": [0, -1.25, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [-9, -17.312, 0], "ti": [0, -1.25, 0], "to": [0, -0.833, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 37, "s": [-9, -9.812, 0], "ti": [0, 0, 0], "to": [0, 0, 0]}, {"t": 47, "s": [-9, -9.812, 0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();", "a": 1, "l": 2}, "a": {"a": 0, "k": [-9, -11.062, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [-0.933, 0], [0, 0], [0, -0.933]], "o": [[0, 0], [0, 0], [0, -0.933], [0, 0], [0.933, 0], [0, 0]], "v": [[-7, -11], [-12, -11], [-12, -21.31], [-10.31, -23], [-8.69, -23], [-7, -21.31]], "c": true}, "a": 0}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 2.76], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [2.76, 0]], "v": [[-7, -20], [-7, -11], [-12, -11], [-12, -15]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.847, 0.266, 0.364, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 50}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [-0.933, 0], [0, 0], [0, -0.933]], "o": [[0, 0], [0, 0], [0, -0.933], [0, 0], [0.933, 0], [0, 0]], "v": [[-7, -11], [-12, -11], [-12, -21.31], [-10.31, -23], [-8.69, -23], [-7, -21.31]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [1, 0.341, 0.431, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 19, "ty": 4}], "markers": []}