{"v": "5.9.6", "ip": 0, "op": 188, "fr": 29, "w": 1080, "h": 1080, "nm": "C", "assets": [], "layers": [{"ind": 1, "nm": "N", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [540, 540, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [800, 800, 100], "l": 2}}, "shapes": [], "ip": 0, "op": 600, "st": 0, "ty": 4}, {"ind": 2, "nm": "1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 13, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15.938, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 39.438, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42.375, "s": [0]}, {"t": 47, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-13.53, -33.97, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[2.53, 1.97], [-2.53, -1.97]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 13, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22.793, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42.375, "s": [100]}, {"t": 47, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 13, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 32.583, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42.375, "s": [100]}, {"t": 47, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 1, "ty": 4}, {"ind": 3, "nm": "2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 18.938, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42.438, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 45.375, "s": [0]}, {"t": 47, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-24.135, -42.215, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[4.865, 3.785], [-4.865, -3.785]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 25.793, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 45.375, "s": [100]}, {"t": 47, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 35.583, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 45.375, "s": [100]}, {"t": 47, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 1, "ty": 4}, {"ind": 4, "nm": "3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 13, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15.938, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 39.438, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42.375, "s": [0]}, {"t": 47, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-8, -37, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[2, 4], [-2, -4]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 13, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22.793, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42.375, "s": [100]}, {"t": 47, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 13, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 32.583, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42.375, "s": [100]}, {"t": 47, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 1, "ty": 4}, {"ind": 5, "nm": "4", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 13, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15.938, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 39.438, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42.375, "s": [0]}, {"t": 47, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, -35.46, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, 2.96], [0, -2.96]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 13, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22.793, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42.375, "s": [100]}, {"t": 47, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 13, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 32.583, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42.375, "s": [100]}, {"t": 47, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 1, "ty": 4}, {"ind": 6, "nm": "5", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 18.938, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42.438, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 45.375, "s": [0]}, {"t": 47, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, -45.915, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, 4.585], [0, -4.585]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 25.793, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 45.375, "s": [100]}, {"t": 47, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 35.583, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 45.375, "s": [100]}, {"t": 47, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 1, "ty": 4}, {"ind": 7, "nm": "6", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 13, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15.938, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 39.438, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42.375, "s": [0]}, {"t": 47, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [8, -37, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-2, 4], [2, -4]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 13, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22.793, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42.375, "s": [100]}, {"t": 47, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 13, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 32.583, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42.375, "s": [100]}, {"t": 47, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 1, "ty": 4}, {"ind": 8, "nm": "7", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 13, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15.938, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 39.438, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42.375, "s": [0]}, {"t": 47, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [13.545, -33.98, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-2.545, 1.98], [2.545, -1.98]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 13, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22.793, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42.375, "s": [100]}, {"t": 47, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 13, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 32.583, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42.375, "s": [100]}, {"t": 47, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 1, "ty": 4}, {"ind": 9, "nm": "8", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 18.938, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42.438, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 45.375, "s": [0]}, {"t": 47, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [24.615, -42.585, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-4.385, 3.415], [4.385, -3.415]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 25.793, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 45.375, "s": [100]}, {"t": 47, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 35.583, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 45.375, "s": [100]}, {"t": 47, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 1, "ty": 4}, {"ind": 10, "nm": "2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [-11.822]}, "o": {"x": [0.167], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.958]}, "o": {"x": [0.167], "y": [0.038]}, "t": 11, "s": [0]}, {"i": {"x": [0.833], "y": [13.499]}, "o": {"x": [0.167], "y": [12.499]}, "t": 14.299, "s": [100]}, {"i": {"x": [0.833], "y": [0.919]}, "o": {"x": [0.167], "y": [0.042]}, "t": 24.191, "s": [100]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [-8.592]}, "t": 27.49, "s": [0]}, {"t": 31, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-46.05, -11.12, 0], "l": 2}, "a": {"a": 0, "k": [-46.05, -11.12, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0.7, -4.72]], "o": [[-4.51, 2.48], [0, 0]], "v": [[-41.98, -21.9], [-50.01, -10.34]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.709, 0.062, 0.203, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0.2, 2.08]], "o": [[-1.07, -1.94], [0, 0]], "v": [[-48.23, -0.34], [-50.12, -6.43]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.709, 0.062, 0.203, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 23, "ty": 4}, {"ind": 11, "nm": "1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [-9.042]}, "o": {"x": [0.167], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.958]}, "o": {"x": [0.167], "y": [0.066]}, "t": 5, "s": [0]}, {"i": {"x": [0.833], "y": [13.499]}, "o": {"x": [0.167], "y": [12.499]}, "t": 8.299, "s": [100]}, {"i": {"x": [0.833], "y": [0.957]}, "o": {"x": [0.167], "y": [0.042]}, "t": 18.191, "s": [100]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [-12.374]}, "t": 21.49, "s": [0]}, {"t": 31, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-39.165, -8.298, 0], "l": 2}, "a": {"a": 0, "k": [-39.165, -8.298, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [-2.125, -3.86]], "o": [[-3.86, 2.125], [0, 0]], "v": [[-37.098, -13.717], [-40.241, -2.88]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.709, 0.062, 0.203, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 23, "ty": 4}, {"ind": 12, "nm": "1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [-11.822]}, "o": {"x": [0.167], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.958]}, "o": {"x": [0.167], "y": [0.038]}, "t": 11, "s": [0]}, {"i": {"x": [0.833], "y": [13.499]}, "o": {"x": [0.167], "y": [12.499]}, "t": 14.299, "s": [100]}, {"i": {"x": [0.833], "y": [0.919]}, "o": {"x": [0.167], "y": [0.042]}, "t": 24.191, "s": [100]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [-8.592]}, "t": 27.49, "s": [0]}, {"t": 31, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [50.791, -10.12, 0], "l": 2}, "a": {"a": 0, "k": [50.791, -10.12, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [1.7, 0.94]], "o": [[-1.19, -1.39], [0, 0]], "v": [[51.03, -17.37], [46.68, -20.9]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.709, 0.062, 0.203, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [2.68, -4.86]], "o": [[2.43, 4.56], [0, 0]], "v": [[53.04, -14.42], [52.93, 0.66]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.709, 0.062, 0.203, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 29, "ty": 4}, {"ind": 13, "nm": "1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [-10.328]}, "o": {"x": [0.167], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.958]}, "o": {"x": [0.167], "y": [0.053]}, "t": 7, "s": [0]}, {"i": {"x": [0.833], "y": [13.499]}, "o": {"x": [0.167], "y": [12.499]}, "t": 10.299, "s": [100]}, {"i": {"x": [0.833], "y": [0.949]}, "o": {"x": [0.167], "y": [0.042]}, "t": 20.191, "s": [100]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [-11.58]}, "t": 23.49, "s": [0]}, {"t": 31, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [43.864, -7.298, 0], "l": 2}, "a": {"a": 0, "k": [43.864, -7.298, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [2.125, -3.86]], "o": [[3.86, 2.125], [0, 0]], "v": [[41.798, -12.717], [44.94, -1.88]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.709, 0.062, 0.203, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 29, "ty": 4}, {"ind": 14, "nm": "1", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-0.454, 45.943, 0], "l": 2}, "a": {"a": 0, "k": [-0.454, 45.943, 0], "l": 2}, "s": {"k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [30, 30, 100]}, {"t": 31, "s": [110, 110, 100]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');", "a": 1, "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [1.934, 0], [-1.934, 0]], "o": [[1.93, 0], [-1.93, 0], [0, 0]], "v": [[-0.454, 47.443], [-0.454, 44.443], [-0.454, 47.443]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 1, "ty": 4}, {"ind": 15, "nm": "1", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -5, "s": [90]}, {"t": 42, "s": [-90]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');"}, "p": {"a": 0, "k": [2, 26, 0], "l": 2}, "a": {"a": 0, "k": [2, 26, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.5, 26.5], [-2.5, 26.5]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.709, 0.062, 0.203, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[2, 22], [2, 31]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.709, 0.062, 0.203, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 1, "ty": 4}, {"ind": 16, "nm": "1", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [-90]}, {"t": 47, "s": [90]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');"}, "p": {"a": 0, "k": [40.696, -35.844, 0], "l": 2}, "a": {"a": 0, "k": [40.696, -35.844, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0.62, 1.486], [-2.806, 1.17], [0.453, -1.511]], "o": [[0.63, -1.346], [1.17, 2.806], [-1.578, 0.658], [0, 0]], "v": [[38.486, -36.493], [38.579, -40.92], [45.772, -37.962], [42.635, -34.497]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.003, 0.352, 0.184, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0.213, 0.51], [-0.51, 0.213], [0.955, 2.29], [-0.51, 0.213], [-0.213, -0.51], [-2.29, 0.955], [-0.213, -0.51], [0.51, -0.213], [-0.955, -2.29], [0.51, -0.213], [0.213, 0.51], [2.29, -0.955]], "o": [[-0.213, -0.51], [2.29, -0.955], [-0.213, -0.51], [0.51, -0.213], [0.955, 2.29], [0.51, -0.213], [0.213, 0.51], [-2.29, 0.955], [0.213, 0.51], [-0.51, 0.213], [-0.955, -2.29], [-0.51, 0.213]], "v": [[34.697, -33.342], [35.235, -34.65], [37.656, -40.535], [38.194, -41.843], [39.502, -41.305], [45.387, -38.885], [46.695, -38.347], [46.157, -37.039], [43.737, -31.153], [43.199, -29.846], [41.891, -30.383], [36.005, -32.804]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.286, 0.756, 0.286, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 1, "ty": 4}, {"ind": 17, "nm": "1", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -15, "s": [-90]}, {"t": 32, "s": [90]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');"}, "p": {"a": 0, "k": [-36, -33, 0], "l": 2}, "a": {"a": 0, "k": [-36, -33, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0.28, 0], [0, 1.93], [-1.93, 0], [-0.43, -1.46]], "o": [[-0.26, 0.07], [-1.93, 0], [0, -1.93], [1.59, 0], [0, 0]], "v": [[-35.7, -30.1], [-36.5, -30], [-40, -33.5], [-36.5, -37], [-33.14, -34.48]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, -1.933], [1.933, 0], [0, 1.933], [-1.933, 0]], "o": [[0, 1.933], [-1.933, 0], [0, -1.933], [1.933, 0]], "v": [[-33, -33.5], [-36.5, -30], [-40, -33.5], [-36.5, -37]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0, 0.85, 0.976, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 1, "ty": 4}, {"ind": 18, "nm": "D", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-20.213, 32.759, 0], "l": 2}, "a": {"a": 0, "k": [-17.805, 34.742, 0], "l": 2}, "s": {"a": 0, "k": [95.238, 95.238, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0.683, -1.2], [-1.2, -0.683], [-0.683, 1.2], [1.2, 0.683]], "o": [[-0.683, 1.2], [1.2, 0.683], [0.683, -1.2], [-1.2, -0.683]], "v": [[-16.241, -2.182], [-15.305, 1.227], [-11.896, 0.292], [-12.831, -3.118]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [1, 0.96, 0.827, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 22, "ty": 4}, {"ind": 19, "nm": "D", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-17.832, 32.759, 0], "l": 2}, "a": {"a": 0, "k": [-17.805, 34.742, 0], "l": 2}, "s": {"a": 0, "k": [95.238, 95.238, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [-1.68, -0.957], [1.68, 0.957]], "o": [[-1.678, -0.955], [1.678, 0.955], [0, 0]], "v": [[-21.943, 4.738], [-20.459, 2.131], [-21.943, 4.738]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.709, 0.062, 0.203, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 22, "ty": 4}, {"ind": 21, "nm": "M", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-17.805, 34.742, 0], "l": 2}, "a": {"a": 0, "k": [-17.805, 34.742, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[-0.01, -0.73], [0, 0], [6.597, -0.092], [0, 0], [2.069, 3.695], [0.258, 0.885], [0.014, 1.091], [0, 0], [0, 0], [-0.73, 0.01], [0, 0], [0, 0]], "o": [[0, 0], [0.092, 6.596], [0, 0], [-4.531, 0.064], [-0.449, -0.794], [-0.29, -1.001], [0, 0], [0, 0], [-0.01, -0.73], [0, 0], [0, 0], [0.73, -0.01]], "v": [[-6.615, -20.984], [-6.362, -2.251], [-18.141, 9.858], [-18.258, 9.858], [-28.832, 3.757], [-29.892, 1.226], [-30.358, -1.924], [-30.595, -19.418], [-30.61, -20.658], [-29.308, -21.993], [-25.494, -22.041], [-7.95, -22.286]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.901, 0.984, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 23, "td": 1, "ty": 4}, {"ind": 22, "nm": "L", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 5, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [-15]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 35, "s": [0]}, {"t": 47, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "p": {"a": 0, "k": [-18.354, -6.91, 0], "l": 2}, "a": {"a": 0, "k": [-18.354, -6.91, 0], "l": 2}, "s": {"a": 0, "k": [105, 105, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0], [6.6, -0.082], [0, 0], [2.199, 2.211], [-1.556, 0.024], [0, 0], [0.365, 8.143]], "o": [[0, 0], [0.101, 6.593], [0, 0], [-3.358, 0.041], [1.371, 0.522], [0, 0], [6.6, -0.082], [0, 0]], "v": [[-6.61, -7.048], [-6.491, -2.247], [-19.335, 9.908], [-19.449, 9.918], [-29.073, 6.401], [-24.648, 7.185], [-24.534, 7.175], [-11.184, -7.034]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.847, 0.266, 0.364, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 50}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0.062]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[-9.767, 0.091], [0, 0], [0, 0], [6.6, -0.082], [0, 0], [2.155, 2.004], [0.62, 2.084], [0.01, 0.028], [0.051, 0.258], [0.035, 0.243], [0.008, 0.05], [0.013, 0.24], [0.004, 0.275], [0, 0], [-1.344, -0.001]], "o": [[0, 0], [0, 0], [0.101, 6.593], [0, 0], [-3.178, 0.039], [-1.573, -1.428], [-0.017, -0.047], [-0.07, -0.251], [-0.054, -0.236], [-0.008, -0.05], [-0.032, -0.233], [-0.023, -0.268], [0, 0], [-0.013, 0.007], [5.62, 0.004]], "v": [[-5.482, -7.057], [-5.479, -7.048], [-5.36, -2.247], [-18.204, 9.908], [-18.318, 9.918], [-27.568, 6.755], [-30.935, 1.393], [-30.963, 1.286], [-31.155, 0.526], [-31.288, -0.192], [-31.311, -0.343], [-31.388, -1.05], [-31.429, -1.865], [-31.337, -6.975], [-28.071, -6.94]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [1, 0.447, 0.588, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 23, "tt": 1, "ty": 4}, {"ind": 23, "nm": "G", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -3, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 2, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 17, "s": [15]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 32, "s": [0]}, {"t": 44, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "p": {"k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0.333}, "t": -3, "s": [337.557, 817.94, 0], "ti": [0, 0, 0], "to": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 2, "s": [337.557, 817.94, 0], "ti": [0, 0, 0], "to": [5.333, -4, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 17, "s": [369.557, 793.94, 0], "ti": [5.333, -4, 0], "to": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [337.557, 817.94, 0], "ti": [0, 0, 0], "to": [0, 0, 0]}, {"t": 44, "s": [337.557, 817.94, 0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();", "a": 1, "l": 2}, "a": {"a": 0, "k": [-17.805, 34.742, 0], "l": 2}, "s": {"a": 0, "k": [800, 800, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0], [-0.73, 0.01], [0, 0], [-0.01, -0.73], [0, 0], [6.596, -0.092], [0, 0], [0.092, 6.596], [0, 0]], "o": [[0, 0], [-0.01, -0.73], [0, 0], [0.73, -0.01], [0, 0], [0.092, 6.596], [0, 0], [-6.596, 0.092], [0, 0], [0, 0]], "v": [[-30.53, -14.706], [-30.61, -20.658], [-29.308, -21.993], [-7.95, -22.286], [-6.615, -20.984], [-6.362, -2.251], [-18.141, 9.858], [-18.258, 9.858], [-30.358, -1.924], [-30.477, -10.352]], "c": false}, "a": 0}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0], [-0.079, -0.014], [0, 0], [0, 0], [-0.004, -0.275], [0, 0], [0.31, 0.004], [0, 0], [0.012, 0.326], [0, 0], [-0.251, 0.07], [0, 0]], "o": [[0, 0], [0.079, -0.018], [0, 0], [0, 0], [0.264, 0.053], [0, 0], [0.009, 0.316], [0, 0], [-0.316, 0.009], [0, 0], [-0.004, -0.275], [0, 0], [0, 0]], "v": [[-22.8, 36.062], [-17.982, 34.883], [-17.739, 34.879], [-11.349, 36.268], [-5.27, 37.588], [-4.822, 38.149], [-4.794, 40.097], [-5.347, 40.681], [-30.212, 41.016], [-30.794, 40.44], [-30.819, 38.502], [-30.383, 37.917], [-26.413, 36.951]], "c": false}, "a": 0}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[-0.004, -0.275], [0, 0], [0.31, 0.004], [0, 0], [0.09, 2.032], [0, 0], [0, 0]], "o": [[0, 0], [0.009, 0.316], [0, 0], [1.749, -0.839], [0, 0], [0, 0], [0.264, 0.053]], "v": [[-4.822, 38.149], [-4.794, 40.097], [-5.347, 40.681], [-14.847, 40.808], [-12.03, 36.122], [-11.349, 36.268], [-5.27, 37.588]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.639, 0.764, 0.807, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 50}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0], [-0.081, -0.018], [0, 0], [0, 0], [-0.004, -0.276], [0, 0], [0.313, -0.004], [0, 0], [0.004, 0.325], [0, 0], [-0.258, 0.063]], "o": [[0, 0], [0.081, -0.02], [0, 0], [0, 0], [0.26, 0.056], [0, 0], [0.004, 0.325], [0, 0], [-0.313, 0.004], [0, 0], [-0.004, -0.276], [0, 0]], "v": [[-24.343, 36.441], [-17.986, 34.884], [-17.74, 34.881], [-11.344, 36.267], [-5.269, 37.583], [-4.818, 38.151], [-4.792, 40.091], [-5.35, 40.686], [-30.215, 41.021], [-30.79, 40.44], [-30.816, 38.501], [-30.38, 37.92]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.901, 0.984, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-17.823, 37.854], [-18.2, 9.856]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[-0.01, -0.73], [0, 0], [6.597, -0.092], [0, 0], [2.069, 3.695], [0.258, 0.885], [0.014, 1.091], [0, 0], [0, 0], [-0.73, 0.01], [0, 0], [0, 0]], "o": [[0, 0], [0.092, 6.596], [0, 0], [-4.531, 0.064], [-0.449, -0.794], [-0.29, -1.001], [0, 0], [0, 0], [-0.01, -0.73], [0, 0], [0, 0], [0.73, -0.01]], "v": [[-6.615, -20.984], [-6.362, -2.251], [-18.141, 9.858], [-18.258, 9.858], [-28.832, 3.757], [-29.892, 1.226], [-30.358, -1.924], [-30.595, -19.418], [-30.61, -20.658], [-29.308, -21.993], [-25.494, -22.041], [-7.95, -22.286]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.901, 0.984, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "ty": 4}, {"ind": 24, "nm": "D", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [22.238, 33.878, 0], "l": 2}, "a": {"a": 0, "k": [22.248, 35.93, 0], "l": 2}, "s": {"a": 0, "k": [95.238, 95.238, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0.748, -1.161], [1.161, 0.748], [-0.748, 1.161], [-1.161, -0.748]], "o": [[-0.748, 1.161], [-1.161, -0.748], [0.748, -1.161], [1.161, 0.748]], "v": [[19.423, 1.339], [15.968, 2.086], [15.221, -1.369], [18.676, -2.116]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [1, 0.96, 0.827, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 28, "ty": 4}, {"ind": 25, "nm": "D", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [22.238, 33.878, 0], "l": 2}, "a": {"a": 0, "k": [22.248, 35.93, 0], "l": 2}, "s": {"a": 0, "k": [95.238, 95.238, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [1.625, 1.048], [-1.625, -1.048]], "o": [[1.623, 1.046], [-1.623, -1.046], [0, 0]], "v": [[23.983, 5.757], [25.608, 3.235], [23.983, 5.757]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.627, 0.305, 0.015, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 28, "ty": 4}, {"ind": 27, "nm": "M", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [22.248, 35.93, 0], "l": 2}, "a": {"a": 0, "k": [22.248, 35.93, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[6.603, -0.044], [0, 0], [0.048, 6.594], [0, 0], [-0.728, 0.012], [0, 0], [0, 0], [0.001, -0.734], [0, 0], [0, 0]], "o": [[0, 0], [-6.603, 0.044], [0, 0], [-0.002, -0.724], [0, 0], [0, 0], [0.737, -0.008], [0, 0], [0, 0], [0.044, 6.603]], "v": [[22.132, 11.107], [22.028, 11.101], [9.999, -0.758], [9.881, -19.499], [11.196, -20.829], [26.013, -20.927], [32.55, -20.964], [33.88, -19.65], [33.916, -13.667], [34.001, -0.919]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.901, 0.984, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 29, "td": 1, "ty": 4}, {"ind": 28, "nm": "L", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 5, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [10]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 35, "s": [0]}, {"t": 47, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "p": {"a": 0, "k": [22.052, -7.176, 0], "l": 2}, "a": {"a": 0, "k": [22.052, -7.176, 0], "l": 2}, "s": {"a": 0, "k": [105, 105, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[-4.972, -1.427], [0.212, 0.003], [0, 0], [0.048, 6.594], [0, 0], [0.089, -0.01], [0, 0]], "o": [[-0.209, 0.02], [0, 0], [-6.6, 0.035], [0, 0], [1.373, -0.007], [0, 0], [0.035, 5.429]], "v": [[22.739, 10.651], [22.111, 10.678], [22.004, 10.681], [9.978, -1.188], [10, -7], [14.008, -7.059], [14.079, -0.759]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.878, 0.592, 0.247, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 50}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[6.594, -0.048], [0, 0], [0.048, 6.594], [0, 0], [-7, 0], [0, 0]], "o": [[0, 0], [-6.6, 0.035], [0, 0], [0.956, -0.142], [0, 0], [0.044, 6.603]], "v": [[22.111, 10.678], [22.004, 10.681], [9.978, -1.188], [10, -7], [34, -7], [33.98, -1.348]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [1, 0.8, 0.329, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 29, "tt": 1, "ty": 4}, {"ind": 29, "nm": "G", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -3, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 2, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 17, "s": [-10]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 32, "s": [0]}, {"t": 44, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "p": {"k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0.333}, "t": -3, "s": [737.982, 827.443, 0], "ti": [0, 0, 0], "to": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 2, "s": [737.982, 827.443, 0], "ti": [0, 0, 0], "to": [-5.333, -5.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 17, "s": [705.982, 795.443, 0], "ti": [-5.333, -5.333, 0], "to": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [737.982, 827.443, 0], "ti": [0, 0, 0], "to": [0, 0, 0]}, {"t": 44, "s": [737.982, 827.443, 0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();", "a": 1, "l": 2}, "a": {"a": 0, "k": [22.248, 35.93, 0], "l": 2}, "s": {"a": 0, "k": [800, 800, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0], [0.001, -0.734], [0, 0], [6.603, -0.044], [0, 0], [0.048, 6.594], [0, 0], [-0.728, 0.012], [0, 0]], "o": [[0, 0], [0.737, -0.008], [0, 0], [0.044, 6.603], [0, 0], [-6.603, 0.044], [0, 0], [-0.002, -0.725], [0, 0], [0, 0]], "v": [[30.801, -21.384], [32.529, -21.393], [33.859, -20.079], [33.98, -1.348], [22.111, 10.678], [22.007, 10.672], [9.978, -1.188], [9.86, -19.928], [11.175, -21.259], [26.887, -21.361]], "c": false}, "a": 0}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0], [-0.088, -0.021], [0, 0], [0, 0], [-0.005, -0.279], [0, 0], [0.31, -0.004], [0, 0], [0.001, 0.32], [0, 0], [-0.255, 0.056], [0, 0]], "o": [[0, 0], [0.079, -0.014], [0, 0], [0, 0], [0.267, 0.055], [0, 0], [0.007, 0.332], [0, 0], [-0.31, 0.004], [0, 0], [0.004, -0.275], [0, 0], [0, 0]], "v": [[16.44, 37.049], [22.102, 35.704], [22.345, 35.707], [28.731, 37.138], [34.793, 38.493], [35.246, 39.062], [35.253, 41.001], [34.696, 41.597], [9.823, 41.749], [9.257, 41.17], [9.241, 39.228], [9.683, 38.654], [12.992, 37.869]], "c": false}, "a": 0}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[-0.757, -1.457], [0, 0], [0.001, 0.32], [0, 0], [-0.255, 0.056], [0, 0], [0, 0]], "o": [[0, 0], [-0.31, 0.004], [0, 0], [0.004, -0.275], [0, 0], [0, 0], [-0.036, 1.679]], "v": [[18.154, 41.695], [9.824, 41.749], [9.257, 41.17], [9.241, 39.228], [9.683, 38.654], [15.73, 37.216], [17.036, 36.903]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.639, 0.764, 0.807, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 50}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0], [0.081, -0.019], [0, 0], [0, 0], [-0.002, -0.276], [0, 0], [-0.313, 0.002], [0, 0], [0.002, 0.325], [0, 0], [0.26, 0.058]], "o": [[0, 0], [-0.081, -0.018], [0, 0], [0, 0], [-0.259, 0.061], [0, 0], [0.002, 0.325], [0, 0], [0.313, -0.002], [0, 0], [-0.002, -0.276], [0, 0]], "v": [[28.73, 37.133], [22.343, 35.702], [22.098, 35.703], [15.73, 37.216], [9.683, 38.653], [9.243, 39.23], [9.255, 41.17], [9.825, 41.754], [34.692, 41.594], [35.255, 41.003], [35.242, 39.063], [34.795, 38.491]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.901, 0.984, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[22.245, 38.672], [22.147, 23.386]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[22.125, 20.026], [22.061, 10.67]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[6.603, -0.044], [0, 0], [0.048, 6.594], [0, 0], [-0.728, 0.012], [0, 0], [0, 0], [0.001, -0.734], [0, 0], [0, 0]], "o": [[0, 0], [-6.603, 0.044], [0, 0], [-0.002, -0.724], [0, 0], [0, 0], [0.737, -0.008], [0, 0], [0, 0], [0.044, 6.603]], "v": [[22.132, 11.107], [22.028, 11.101], [9.999, -0.758], [9.881, -19.499], [11.196, -20.829], [26.013, -20.927], [32.55, -20.964], [33.88, -19.65], [33.916, -13.667], [34.001, -0.919]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.901, 0.984, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "ty": 4}], "markers": []}