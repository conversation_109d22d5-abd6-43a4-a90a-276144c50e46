//
//  CardTransferService.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/31.
//

import Foundation

// MARK: - 卡片传输服务协议

/// 卡片传输服务协议
protocol CardTransferServiceProtocol {
    /// 创建卡片传输请求
    func createCardTransfer(senderId: String, receiverId: String, cardId: String) async throws -> CardTransferRecord

    /// 处理传输请求（接受或拒绝）
    func processCardTransfer(transferId: String, action: String, userId: String) async throws -> CardTransferRecord

    /// 查询接收到的传输记录
    func getReceivedTransfers(userId: String, status: CardTransferStatus?) async throws -> [CardTransferRecord]

    /// 查询发送的传输记录
    func getSentTransfers(userId: String, status: CardTransferStatus?) async throws -> [CardTransferRecord]
}

// MARK: - 卡片传输服务实现

/// 卡片传输服务
class CardTransferService: CardTransferServiceProtocol {

    // MARK: - Properties
    private let baseURL = AuthConfig.baseURL
    private let jsonDecoder: JSONDecoder
    private let jsonEncoder: JSONEncoder

    // MARK: - Initialization
    init() {
        self.jsonDecoder = JSONDecoder()
        self.jsonEncoder = JSONEncoder()

        // 配置日期解析格式
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        dateFormatter.locale = Locale(identifier: "en_US_POSIX")
        dateFormatter.timeZone = TimeZone(secondsFromGMT: 0)

        jsonDecoder.dateDecodingStrategy = .custom { decoder in
            let container = try decoder.singleValueContainer()
            let dateString = try container.decode(String.self)

            // 尝试多种日期格式
            let formatters = [
                dateFormatter,
            ]

            for formatter in formatters {
                if let date = formatter.date(from: dateString) {
                    return date
                }
            }

            throw DecodingError.dataCorruptedError(in: container, debugDescription: "无法解析日期: \(dateString)")
        }
    }

    // MARK: - API Response Models

    /// API通用响应模型
    private struct APIResponse<T: Codable>: Codable {
        let success: Bool
        let data: T?
        let message: String?
        let error: String?
    }

    /// API返回的传输记录结构
    private struct CardTransferAPI: Codable {
        let id: String
        let senderId: String
        let receiverId: String
        let cardId: String
        let transferTime: String
        let status: String
        let sender: UserInfoAPI?
        let receiver: UserInfoAPI?
        let card: CardInfoAPI?

        struct UserInfoAPI: Codable {
            let userId: String
            let nickname: String
            let avatarURL: String?
        }

        struct CardInfoAPI: Codable {
            let id: String
            let title: String
            let description: String
            let imageURL: String
            let cardType: String // 卡片类型：scenery 或 shopping
            let themeColor: String? // 主题色（仅购物卡片）
            let coinReward: Int // 碳币奖励
            let experienceReward: Int // 经验奖励
            let location: String // 位置信息
            let createdAt: String // 创建时间
            let authorId: String? // 原始作者ID（用于保持作者信息）
        }
    }

    /// API返回的传输记录列表结构
    private struct CardTransferListAPI: Codable {
        let transfers: CardTransfersByStatusAPI
        let total: Int

        struct CardTransfersByStatusAPI: Codable {
            let pending: [CardTransferAPI]
            let accepted: [CardTransferAPI]
            let rejected: [CardTransferAPI]
        }
    }

    // MARK: - Public Methods

    /// 创建卡片传输请求
    /// - Parameters:
    ///   - senderId: 发送者用户ID
    ///   - receiverId: 接收者用户ID
    ///   - cardId: 要传输的卡片ID
    /// - Returns: 创建的传输记录
    func createCardTransfer(senderId: String, receiverId: String, cardId: String) async throws -> CardTransferRecord {
        guard !senderId.isEmpty, !receiverId.isEmpty, !cardId.isEmpty else {
            throw CardTransferError.missingParameters
        }

        guard let url = URL(string: "\(baseURL)itemcard-transfers") else {
            throw CardTransferError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        let requestBody = CreateCardTransferRequest(
            senderId: senderId,
            receiverId: receiverId,
            cardId: cardId
        )

        do {
            request.httpBody = try jsonEncoder.encode(requestBody)
        } catch {
            throw CardTransferError.decodingError(error)
        }

        do {
            let (data, response) = try await URLSession.shared.data(for: request)

            // 检查HTTP状态码
            if let httpResponse = response as? HTTPURLResponse {
                switch httpResponse.statusCode {
                case 200:
                    break
                case 400:
                    let errorMessage = String(data: data, encoding: .utf8) ?? "参数错误"
                    if errorMessage.contains("重复") || errorMessage.contains("duplicate") {
                        throw CardTransferError.duplicateTransfer
                    } else {
                        throw CardTransferError.serverError(errorMessage)
                    }
                case 404:
                    let errorMessage = String(data: data, encoding: .utf8) ?? "资源不存在"
                    if errorMessage.contains("用户") || errorMessage.contains("user") {
                        throw CardTransferError.userNotFound
                    } else {
                        throw CardTransferError.cardNotFound
                    }
                case 500:
                    throw CardTransferError.serverError("服务器内部错误")
                default:
                    throw CardTransferError.serverError("HTTP \(httpResponse.statusCode)")
                }
            }

            let apiResponse = try jsonDecoder.decode(APIResponse<CardTransferAPI>.self, from: data)
            

            if apiResponse.success, let apiTransfer = apiResponse.data {
                return try convertAPITransferToModel(apiTransfer)
            } else {
                throw CardTransferError.serverError(apiResponse.error ?? "创建传输请求失败")
            }

        } catch let error as CardTransferError {
            throw error
        } catch {
            if error is DecodingError {
                throw CardTransferError.decodingError(error)
            } else {
                throw CardTransferError.networkError(error)
            }
        }
    }

    /// 处理传输请求（接受或拒绝）
    /// - Parameters:
    ///   - transferId: 传输记录ID
    ///   - action: 操作类型（"accept" 或 "reject"）
    ///   - userId: 当前用户ID（必须是接收者）
    /// - Returns: 处理后的传输记录
    func processCardTransfer(transferId: String, action: String, userId: String) async throws -> CardTransferRecord {
        guard !transferId.isEmpty, !action.isEmpty, !userId.isEmpty else {
            throw CardTransferError.missingParameters
        }

        guard action == "accept" || action == "reject" else {
            throw CardTransferError.missingParameters
        }

        guard let url = URL(string: "\(baseURL)itemcard-transfers") else {
            throw CardTransferError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "PATCH"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        let requestBody = ProcessCardTransferRequest(
            transferId: transferId,
            action: action,
            userId: userId
        )

        do {
            request.httpBody = try jsonEncoder.encode(requestBody)
        } catch {
            throw CardTransferError.decodingError(error)
        }

        do {
            let (data, response) = try await URLSession.shared.data(for: request)

            // 检查HTTP状态码
            if let httpResponse = response as? HTTPURLResponse {
                switch httpResponse.statusCode {
                case 200:
                    break
                case 400:
                    let errorMessage = String(data: data, encoding: .utf8) ?? "参数错误或请求已处理"
                    throw CardTransferError.serverError(errorMessage)
                case 403:
                    throw CardTransferError.unauthorizedAccess
                case 404:
                    throw CardTransferError.transferNotFound
                case 500:
                    throw CardTransferError.serverError("服务器内部错误")
                default:
                    throw CardTransferError.serverError("HTTP \(httpResponse.statusCode)")
                }
            }

            let apiResponse = try jsonDecoder.decode(APIResponse<CardTransferAPI>.self, from: data)

            if apiResponse.success, let apiTransfer = apiResponse.data {
                return try convertAPITransferToModel(apiTransfer)
            } else {
                throw CardTransferError.serverError(apiResponse.error ?? "处理传输请求失败")
            }

        } catch let error as CardTransferError {
            throw error
        } catch {
            if error is DecodingError {
                throw CardTransferError.decodingError(error)
            } else {
                throw CardTransferError.networkError(error)
            }
        }
    }

    /// 查询接收到的传输记录
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - status: 可选的状态过滤
    /// - Returns: 接收到的传输记录列表
    func getReceivedTransfers(userId: String, status: CardTransferStatus?) async throws -> [CardTransferRecord] {
        guard !userId.isEmpty else {
            throw CardTransferError.missingParameters
        }

        var urlString = "\(baseURL)itemcard-transfers/received?userId=\(userId)"
        if let status = status {
            urlString += "&status=\(status.rawValue)"
        }

        guard let url = URL(string: urlString) else {
            throw CardTransferError.invalidURL
        }

        do {
            let (data, response) = try await URLSession.shared.data(from: url)

            // 检查HTTP状态码
            if let httpResponse = response as? HTTPURLResponse {
                switch httpResponse.statusCode {
                case 200:
                    break
                case 400:
                    throw CardTransferError.missingParameters
                case 404:
                    throw CardTransferError.userNotFound
                case 500:
                    throw CardTransferError.serverError("服务器内部错误")
                default:
                    throw CardTransferError.serverError("HTTP \(httpResponse.statusCode)")
                }
            }

            // 根据是否指定了status来解析不同的响应格式
            if status != nil {
                // 指定了status，直接返回数组
                let apiTransfers = try jsonDecoder.decode([CardTransferAPI].self, from: data)
                return try apiTransfers.map { try convertAPITransferToModel($0) }
            } else {
                // 未指定status，返回按状态分组的结构
                let apiResponse = try jsonDecoder.decode(APIResponse<CardTransferListAPI>.self, from: data)

                if apiResponse.success, let apiData = apiResponse.data {
                    var allTransfers: [CardTransferRecord] = []

                    // 合并所有状态的传输记录
                    allTransfers.append(contentsOf: try apiData.transfers.pending.map { try convertAPITransferToModel($0) })
                    allTransfers.append(contentsOf: try apiData.transfers.accepted.map { try convertAPITransferToModel($0) })
                    allTransfers.append(contentsOf: try apiData.transfers.rejected.map { try convertAPITransferToModel($0) })

                    return allTransfers
                } else {
                    throw CardTransferError.serverError(apiResponse.error ?? "获取接收传输记录失败")
                }
            }

        } catch let error as CardTransferError {
            throw error
        } catch {
            if error is DecodingError {
                throw CardTransferError.decodingError(error)
            } else {
                throw CardTransferError.networkError(error)
            }
        }
    }

    /// 查询发送的传输记录
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - status: 可选的状态过滤
    /// - Returns: 发送的传输记录列表
    func getSentTransfers(userId: String, status: CardTransferStatus?) async throws -> [CardTransferRecord] {
        guard !userId.isEmpty else {
            throw CardTransferError.missingParameters
        }

        var urlString = "\(baseURL)itemcard-transfers/sent?userId=\(userId)"
        if let status = status {
            urlString += "&status=\(status.rawValue)"
        }

        guard let url = URL(string: urlString) else {
            throw CardTransferError.invalidURL
        }

        do {
            let (data, response) = try await URLSession.shared.data(from: url)

            // 检查HTTP状态码
            if let httpResponse = response as? HTTPURLResponse {
                switch httpResponse.statusCode {
                case 200:
                    break
                case 400:
                    throw CardTransferError.missingParameters
                case 404:
                    throw CardTransferError.userNotFound
                case 500:
                    throw CardTransferError.serverError("服务器内部错误")
                default:
                    throw CardTransferError.serverError("HTTP \(httpResponse.statusCode)")
                }
            }

            // 根据是否指定了status来解析不同的响应格式
            if status != nil {
                // 指定了status，直接返回数组
                let apiTransfers = try jsonDecoder.decode([CardTransferAPI].self, from: data)
                return try apiTransfers.map { try convertAPITransferToModel($0) }
            } else {
                // 未指定status，返回按状态分组的结构
                let apiResponse = try jsonDecoder.decode(APIResponse<CardTransferListAPI>.self, from: data)

                if apiResponse.success, let apiData = apiResponse.data {
                    var allTransfers: [CardTransferRecord] = []

                    // 合并所有状态的传输记录
                    allTransfers.append(contentsOf: try apiData.transfers.pending.map { try convertAPITransferToModel($0) })
                    allTransfers.append(contentsOf: try apiData.transfers.accepted.map { try convertAPITransferToModel($0) })
                    allTransfers.append(contentsOf: try apiData.transfers.rejected.map { try convertAPITransferToModel($0) })

                    return allTransfers
                } else {
                    throw CardTransferError.serverError(apiResponse.error ?? "获取发送传输记录失败")
                }
            }

        } catch let error as CardTransferError {
            throw error
        } catch {
            if error is DecodingError {
                throw CardTransferError.decodingError(error)
            } else {
                throw CardTransferError.networkError(error)
            }
        }
    }

    // MARK: - Private Helper Methods

    /// 将API返回的传输记录转换为模型
    /// - Parameter apiTransfer: API返回的传输记录
    /// - Returns: 转换后的传输记录模型
    private func convertAPITransferToModel(_ apiTransfer: CardTransferAPI) throws -> CardTransferRecord {
        // 解析传输时间
        let transferTime: Date
        if let date = apiTransfer.transferTime.toDate() {
            transferTime = date
        } else {
            throw CardTransferError.decodingError(NSError(domain: "DateParsingError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法解析传输时间: \(apiTransfer.transferTime)"]))
        }

        // 解析状态
        guard let status = CardTransferStatus(rawValue: apiTransfer.status) else {
            throw CardTransferError.decodingError(NSError(domain: "StatusParsingError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法解析传输状态: \(apiTransfer.status)"]))
        }

        // 转换发送者信息
        let sender: UserInfo?
        if let apiSender = apiTransfer.sender {
            sender = UserInfo(
                userId: apiSender.userId,
                nickname: apiSender.nickname,
                avatarURL: apiSender.avatarURL
            )
        } else {
            sender = nil
        }

        // 转换接收者信息
        let receiver: UserInfo?
        if let apiReceiver = apiTransfer.receiver {
            receiver = UserInfo(
                userId: apiReceiver.userId,
                nickname: apiReceiver.nickname,
                avatarURL: apiReceiver.avatarURL
            )
        } else {
            receiver = nil
        }

        // 转换卡片信息
        let card: CardInfo?
        if let apiCard = apiTransfer.card {
            // 解析创建时间
            let createdAt: Date
            if let date = apiCard.createdAt.toDate() {
                createdAt = date
            } else {
                throw CardTransferError.decodingError(NSError(domain: "DateParsingError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法解析卡片创建时间: \(apiCard.createdAt)"]))
            }

            card = CardInfo(
                id: apiCard.id,
                title: apiCard.title,
                description: apiCard.description,
                imageURL: apiCard.imageURL,
                cardType: apiCard.cardType,
                themeColor: apiCard.themeColor,
                coinReward: apiCard.coinReward,
                experienceReward: apiCard.experienceReward,
                location: apiCard.location,
                createdAt: createdAt,
                authorId: apiCard.authorId
            )
        } else {
            card = nil
        }

        return CardTransferRecord(
            id: apiTransfer.id,
            senderId: apiTransfer.senderId,
            receiverId: apiTransfer.receiverId,
            cardId: apiTransfer.cardId,
            transferTime: transferTime,
            status: status,
            sender: sender,
            receiver: receiver,
            card: card
        )
    }
}
