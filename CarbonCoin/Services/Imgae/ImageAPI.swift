//
//  ImageAPI.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/23.
//

import Foundation
import SwiftUI

// MARK: - Gemini API 请求模型
struct GeminiRequest: Encodable {
    struct Content: Encodable {
        struct Part: Encodable {
            struct InlineData: Encodable {
                let mime_type: String
                let data: String
            }
            let inline_data: InlineData?
            let text: String?
        }
        let parts: [Part]
    }
    let contents: [Content]
}

// MARK: - Gemini API 响应模型
struct GeminiResponse: Decodable {
    struct Candidate: Decodable {
        struct Content: Decodable {
            struct Part: Decodable {
                let text: String
            }
            let parts: [Part]
        }
        let content: Content
    }
    let candidates: [Candidate]
}

// MARK: - 解析的输出模型
struct ImageAnalysisResult: Decodable, Equatable {
    let Description: String
    let Title: String
    let Eco_friendly: Int      // 环保指数 (1-100)
    let Pack_value: Int        // 包装指数 (1-100)
}

// MARK: - Gemini API 服务类
@MainActor
class GeminiImageAnalysisService: ObservableObject {
    @Published var description: String = ""
    @Published var title: String = ""
    @Published var ecoFriendly: Int = 0
    @Published var packValue: Int = 0
    @Published var isLoading = false
    @Published var errorMessage: String? = nil

    // 默认API key（开发阶段使用）
    private let defaultAPIKey = "AIzaSyCej2y_ixfxI0ZxkIKSKuoSq0m7wtoJ7XE"

    /// 分析图像内容，返回完整的分析结果
    /// - Parameters:
    ///   - image: 要分析的UIImage
    ///   - customApiKey: 自定义API key，如果为nil则使用默认key
    ///   - cardType: 卡片类型，用于决定是否需要环保和包装指数
    /// - Returns: 分析结果，包含描述、标题和指数（如果是购物卡片）
    func analyzeImage(_ image: UIImage, customApiKey: String? = nil, cardType: CardType) async -> ImageAnalysisResult? {
        isLoading = true
        errorMessage = nil

        // 准备API URL
        guard let url = URL(string: "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent") else {
            errorMessage = "无效的API URL"
            isLoading = false
            return nil
        }

        // 使用自定义API key或默认key
        let apiKey = customApiKey ?? defaultAPIKey

        // 将UIImage转换为JPEG Data并编码为Base64
        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            errorMessage = "图像数据转换失败"
            isLoading = false
            return nil
        }

        let base64String = imageData.base64EncodedString()

        // 构建请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(apiKey, forHTTPHeaderField: "x-goog-api-key")

        // MARK: 构建提示词
        let prompt: String

        if cardType == .shopping {
            // 购物卡片需要环保和包装指数
            prompt = """
            # ROLE
            You are an expert in image content recognition and environmental analysis.

            # GOAL
            Your task is to analyze an input image of a product and provide:
            1. A detailed description (2-3 sentences)
            2. A concise title
            3. Environmental friendliness score (1-100, higher is better)
            4. Packaging simplicity score (1-100, higher means less packaging)

            # SCORING CRITERIA
            - Eco_friendly: Consider materials, sustainability, recyclability, environmental impact
            - Pack_value: Evaluate packaging complexity, waste reduction, minimal packaging design

            # EXAMPLE
            ## Your output
            {
              "Description": "这是一个星巴克品牌的咖啡杯，采用纸质材料制作，表面印有品牌标识。杯子设计简洁，适合外带使用。",
              "Title": "星巴克纸质咖啡杯",
              "Eco_friendly": 75,
              "Pack_value": 85
            }

            # NOTICE
            - Description should be 2-3 sentences, detailed but concise
            - Title should be clear and descriptive
            - Scores must be integers between 1-100
            - Only output valid JSON with exactly these keys
            - Keep your result in Chinese

            Now, analyze the provided image and output the result in the requested JSON format.
            """
        } else {
            // 风景卡片只需要描述和标题
            prompt = """
            # ROLE
            You are an expert in image content recognition and scenic analysis.

            # GOAL
            Your task is to analyze an input scenic image and provide:
            1. A detailed description (2-3 sentences)
            2. A concise title
            3. Set environmental scores to default values

            # EXAMPLE
            ## Your output
            {
              "Description": "这是一片美丽的湖泊景色，湖水清澈见底，周围环绕着青山绿树。阳光透过云层洒在湖面上，形成波光粼粼的美景。",
              "Title": "宁静的山湖美景",
              "Eco_friendly": 100,
              "Pack_value": 100
            }

            # NOTICE
            - Description should be 2-3 sentences, vivid and descriptive
            - Title should capture the essence of the scene
            - For scenic images, always set both scores to 100
            - Only output valid JSON with exactly these keys
            - Keep your result in Chinese

            Now, analyze the provided scenic image and output the result in the requested JSON format.
            """
        }

        // 构建请求体
        let requestBody = GeminiRequest(
            contents: [
                .init(parts: [
                    .init(inline_data: .init(mime_type: "image/jpeg", data: base64String), text: nil),
                    .init(inline_data: nil, text: prompt) 
                ])
            ]
        )

        do {
            request.httpBody = try JSONEncoder().encode(requestBody)
        } catch {
            errorMessage = "请求编码失败: \(error.localizedDescription)"
            isLoading = false
            return nil
        }

        do {
        // MARK: 解析请求
            let (data, response) = try await URLSession.shared.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse else {
                errorMessage = "无效的响应格式"
                isLoading = false
                return nil
            }

            guard (200...299).contains(httpResponse.statusCode) else {
                // 尝试解析错误响应以获取更多详细信息
                var errorDetail = "无详细错误信息"
                if let errorResponse = String(data: data, encoding: .utf8) {
                    errorDetail = errorResponse
                }
                errorMessage = "服务器错误: HTTP \(httpResponse.statusCode) - \(errorDetail)"
                print("Gemini API 错误详情: \(errorDetail)")
                isLoading = false
                return nil
            }

            // 解析Gemini API响应
            let geminiResponse = try JSONDecoder().decode(GeminiResponse.self, from: data)

            guard let jsonString = geminiResponse.candidates.first?.content.parts.first?.text else {
                errorMessage = "API响应中未找到文本内容"
                isLoading = false
                return nil
            }
            
            // 清理可能的 Markdown 代码块格式
            let cleanedJsonString = jsonString.trimmingCharacters(in: .whitespacesAndNewlines)
                .replacingOccurrences(of: "^```json\\s*", with: "", options: .regularExpression)
                .replacingOccurrences(of: "\\s*```$", with: "", options: .regularExpression)
            
            print("清理后的JSON字符串: \(cleanedJsonString)")

            // 二次解码JSON字符串为ImageAnalysisResult
            guard let jsonData = cleanedJsonString.data(using: .utf8),
                  let result = try? JSONDecoder().decode(ImageAnalysisResult.self, from: jsonData) else {
                errorMessage = "解析分析结果失败，API返回格式不正确"
                print("解析失败的原始响应内容: \(jsonString)")
                print("清理后的JSON字符串: \(cleanedJsonString)")
                isLoading = false
                return nil
            }

            // 更新发布的属性
            description = result.Description
            title = result.Title
            ecoFriendly = result.Eco_friendly
            packValue = result.Pack_value

            isLoading = false
            return result

        } catch let error as URLError where error.code == .notConnectedToInternet {
            errorMessage = "网络连接失败，请检查网络设置"
            print("网络错误: \(error.localizedDescription)")
            isLoading = false
            return nil
        } catch {
            errorMessage = "网络请求失败: \(error.localizedDescription)"
            print("网络请求错误: \(error)")
            isLoading = false
            return nil
        }
    }
}

// MARK: - Dify API 请求和响应模型

struct DifyFileUploadResponse: Decodable {
    let id: String
    let name: String
    let size: Int
    let `extension`: String
    let mime_type: String
    let created_by: String
    let created_at: Int
}

struct DifyWorkflowRequest: Encodable {
    struct Inputs: Encodable {
        struct ImageInput: Encodable {
            let transfer_method: String
            let upload_file_id: String
            let type: String
        }
        let imageToClassify: ImageInput
    }

    let inputs: Inputs
    let response_mode: String
    let user: String
}

struct DifyWorkflowResponse: Decodable {
    struct Data: Decodable {
        struct Outputs: Decodable {
            let Description: String?
            let Title: String?
            let Eco_friendly: Int?
            let Pack_value: Int?
        }
        let outputs: Outputs
    }
    let data: Data
    let workflow_run_id: String
    let task_id: String
}

// MARK: - Dify API 服务类
@MainActor
class DifyImageAnalysisService: ObservableObject {
    @Published var description: String = ""
    @Published var title: String = ""
    @Published var ecoFriendly: Int = 0
    @Published var packValue: Int = 0
    @Published var isLoading = false
    @Published var errorMessage: String? = nil

    // Dify API配置
    private let apiKey = "app-U4A2eE9Qk0j9JM7eE6ZgYmXg"
    private let uploadURL = "https://api.dify.ai/v1/files/upload"
    private let workflowURL = "https://api.dify.ai/v1/workflows/run"

    /// 分析图像内容，使用Dify工作流
    /// - Parameters:
    ///   - image: 要分析的UIImage
    ///   - cardType: 卡片类型
    /// - Returns: 分析结果
    func analyzeImage(_ image: UIImage, cardType: CardType) async -> ImageAnalysisResult? {
        isLoading = true
        errorMessage = nil

        do {
            // 第一步：上传文件获取file_id
            guard let fileId = await uploadImage(image) else {
                isLoading = false
                return nil
            }

            // 第二步：调用工作流进行分析
            let result = await runWorkflow(fileId: fileId, cardType: cardType)

            if let result = result {
                // 更新发布的属性
                description = result.Description
                title = result.Title
                ecoFriendly = result.Eco_friendly
                packValue = result.Pack_value
            }

            isLoading = false
            return result

        } catch {
            errorMessage = "Dify分析失败: \(error.localizedDescription)"
            print("Dify分析错误: \(error)")
            isLoading = false
            return nil
        }
    }

    /// 上传图片到Dify平台
    private func uploadImage(_ image: UIImage) async -> String? {
        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            errorMessage = "图像数据转换失败"
            return nil
        }

        guard let url = URL(string: uploadURL) else {
            errorMessage = "无效的上传URL"
            return nil
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")

        // 创建multipart/form-data请求体
        let boundary = UUID().uuidString
        request.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")

        var body = Data()

        // 添加文件数据
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"file\"; filename=\"image.jpg\"\r\n".data(using: .utf8)!)
        body.append("Content-Type: image/jpeg\r\n\r\n".data(using: .utf8)!)
        body.append(imageData)
        body.append("\r\n".data(using: .utf8)!)

        // 添加用户字段
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"user\"\r\n\r\n".data(using: .utf8)!)
        body.append("abc-123".data(using: .utf8)!)
        body.append("\r\n".data(using: .utf8)!)

        body.append("--\(boundary)--\r\n".data(using: .utf8)!)

        request.httpBody = body

        do {
            let (data, response) = try await URLSession.shared.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse else {
                errorMessage = "无效的响应格式"
                return nil
            }

            guard (200...299).contains(httpResponse.statusCode) else {
                let errorDetail = String(data: data, encoding: .utf8) ?? "无详细错误信息"
                errorMessage = "文件上传失败: HTTP \(httpResponse.statusCode) - \(errorDetail)"
                print("Dify文件上传错误: \(errorDetail)")
                return nil
            }

            let uploadResponse = try JSONDecoder().decode(DifyFileUploadResponse.self, from: data)
            print("✅ 文件上传成功，文件ID: \(uploadResponse.id)")
            return uploadResponse.id

        } catch {
            errorMessage = "文件上传网络错误: \(error.localizedDescription)"
            print("Dify文件上传网络错误: \(error)")
            return nil
        }
    }

    /// 调用Dify工作流进行图像分析
    private func runWorkflow(fileId: String, cardType: CardType) async -> ImageAnalysisResult? {
        guard let url = URL(string: workflowURL) else {
            errorMessage = "无效的工作流URL"
            return nil
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")

        // 构建请求体
        let workflowRequest = DifyWorkflowRequest(
            inputs: DifyWorkflowRequest.Inputs(
                imageToClassify: DifyWorkflowRequest.Inputs.ImageInput(
                    transfer_method: "local_file",
                    upload_file_id: fileId,
                    type: "image"
                )
            ),
            response_mode: "blocking",
            user: "abc-123"
        )

        do {
            request.httpBody = try JSONEncoder().encode(workflowRequest)
        } catch {
            errorMessage = "工作流请求编码失败: \(error.localizedDescription)"
            return nil
        }

        do {
            let (data, response) = try await URLSession.shared.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse else {
                errorMessage = "无效的响应格式"
                return nil
            }

            guard (200...299).contains(httpResponse.statusCode) else {
                let errorDetail = String(data: data, encoding: .utf8) ?? "无详细错误信息"
                errorMessage = "工作流调用失败: HTTP \(httpResponse.statusCode) - \(errorDetail)"
                print("Dify工作流错误: \(errorDetail)")
                return nil
            }

            let workflowResponse = try JSONDecoder().decode(DifyWorkflowResponse.self, from: data)
            
            let outputs = workflowResponse.data.outputs

            // 根据卡片类型处理结果
            let result = ImageAnalysisResult(
                Description: outputs.Description ?? "无法获取描述",
                Title: outputs.Title ?? "未知标题",
                Eco_friendly: cardType == .shopping ? (outputs.Eco_friendly ?? 50) : 100,
                Pack_value: cardType == .shopping ? (outputs.Pack_value ?? 50) : 100
            )

            print("✅ Dify工作流分析完成: \(result)")
            return result

        } catch {
            errorMessage = "工作流调用网络错误: \(error.localizedDescription)"
            print("Dify工作流网络错误: \(error)")
            return nil
        }
    }
}
