//
//  CommonUtils.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/8.
//

import Foundation
import SwiftUI

// MARK: 格式化日期
func formatDate(_ date: Date) -> String {
    let formatter = DateFormatter()
    formatter.locale = Locale(identifier: "zh_CN")

    let calendar = Calendar.current
    if calendar.isDateInToday(date) {
        return "今天"
    } else if calendar.isDateInYesterday(date) {
        return "昨天"
    } else {
        formatter.dateFormat = "yy/M/d"
        return formatter.string(from: date)
    }
}

// Helper for specific corner radius
struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(roundedRect: rect, byRoundingCorners: corners, cornerRadii: CGSize(width: radius, height: radius))
        return Path(path.cgPath)
    }
}
